{"firestore": {"collections": {"cases": {"[CASE_ID]": {"case_number": "String", "naf_info": {"name": "String", "phone": "String", "email": "String", "address": {"street": "String", "suburb": "String", "state": "String", "postcode": "String"}, "claim_number": "String", "insurer": "String", "license": "String", "vehicle_reg": "String"}, "af_info": {"name": "String", "phone": "String", "email": "String", "address": {"street": "String", "suburb": "String", "state": "String", "postcode": "String"}, "claim_number": "String", "insurer_id": "String", "license": "String", "vehicle_reg": "String"}, "accident_details": {"date": "Timestamp", "time": "String", "location": "String"}, "status": "String", "assigned_bike_id": "String", "assigned_collections_id": "String", "notes": "String", "financial_summary": {"invoiced": "Number", "reserve": "Number", "settled": "Number", "paid": "Number"}, "created_at": "Timestamp", "updated_at": "Timestamp", "financial_records": {"[FINANCIAL_RECORD_ID]": {"invoiced": "Number", "reserve": "Number", "settlement_agreed": "Number", "paid": "Number", "settled": "Number", "created_at": "Timestamp", "updated_at": "Timestamp"}}, "bike_assignments": {"[BIKE_ASSIGNMENT_ID]": {"bike_id": "String", "rate_a": "Number", "rate_b": "Number", "assigned_date": "Timestamp", "returned_date": "Timestamp", "created_at": "Timestamp"}}, "documents": {"[DOCUMENT_ID]": {"filename": "String", "filepath": "String", "file_type": "String", "uploaded_at": "Timestamp"}}, "followup_notes": {"[FOLLOWUP_NOTE_ID]": {"note_content": "String", "created_at": "Timestamp", "updated_at": "Timestamp"}}, "communication_logs": {"[COMMUNICATION_LOG_ID]": {"log_type": "String", "subject": "String", "content": "String", "contact_name": "String", "contact_email": "String", "contact_phone": "String", "direction": "String", "duration_minutes": "Number", "tags": ["String"], "priority": "String", "status": "String", "follow_up_required": "Boolean", "follow_up_date": "Timestamp", "created_by": "String", "created_at": "Timestamp"}}, "rental_agreements": {"[RENTAL_AGREEMENT_ID]": {"agreement_number": "String", "bike_info": {"make": "String", "model": "String"}, "rental_period": {"hire_date": "Timestamp", "hire_time": "String", "return_date": "Timestamp", "return_time": "String"}, "hirer1_info": {}, "hirer2_info": {}, "financial_info": {}, "status": "String", "created_at": "Timestamp", "signatures": {"[SIGNATURE_ID]": {"signature_type": "String", "signature_data": "String", "signature_hash": "String", "signer_name": "String", "signer_email": "String", "ip_address": "String", "user_agent": "String", "device_fingerprint": {}, "signed_at": "Timestamp", "timezone": "String", "terms_accepted": "Boolean", "verification_code": "String"}}}}}}, "bikes": {"[BIKE_ID]": {"make": "String", "model": "String", "registration": "String", "registration_expiry": "Timestamp", "assigned_case_id": "String", "service_center": "String", "delivery_address": {"street": "String", "suburb": "String", "state": "String", "postcode": "String"}, "last_service_date": "Timestamp", "service_notes": "String", "created_at": "Timestamp"}}, "insurance_companies": {"[INSURANCE_ID]": {"name": "String", "phone": "String", "email": "String", "notes": "String", "created_at": "Timestamp"}}, "collections_clients": {"[COLLECTIONS_CLIENT_ID]": {"company_name": "String", "contact_name": "String", "contact_email": "String", "phone": "String", "created_at": "Timestamp", "updated_at": "Timestamp"}}, "signature_tokens": {"[TOKEN_HASH]": {"case_id": "String", "client_email": "String", "form_data": {}, "status": "String", "expires_at": "Timestamp", "signed_at": "Timestamp", "completed_at": "Timestamp", "business_email": "String", "document_type": "String", "form_link": "String"}}}}}