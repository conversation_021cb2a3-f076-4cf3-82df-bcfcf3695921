<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en-US"  class="supernova "><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="alternate" type="application/json+oembed" href="https://www.jotform.com/oembed/?format=json&amp;url=https%3A%2F%2Fform.jotform.com%2F233183619631457" title="oEmbed Form">
<link rel="alternate" type="text/xml+oembed" href="https://www.jotform.com/oembed/?format=xml&amp;url=https%3A%2F%2Fform.jotform.com%2F233183619631457" title="oEmbed Form">
<meta property="og:title" content="NotAtFault Claims Form" >
<meta property="og:url" content="https://render.jotform.com/233183619631457" >
<meta property="og:description" content="Please click the link to complete this form." >
<meta name="slack-app-id" content="AHNMASS8M">
<meta property="og:image" content="https://cdn.jotfor.ms/assets/img/landing/opengraph.png" />
<link rel="shortcut icon" href="https://cdn.jotfor.ms/assets/img/favicons/favicon-2021-light%402x.png">
<link rel="apple-touch-icon" href="https://cdn.jotfor.ms/assets/img/favicons/favicon-2021-light%402x.png">
<script>
          var favicon = document.querySelector('link[rel="shortcut icon"]');
          window.isDarkMode = (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches);
          if(favicon && window.isDarkMode) {
              favicon.href = favicon.href.replaceAll('favicon-2021-light%402x.png', 'favicon-2021-dark%402x.png');
          }
      </script><link rel="canonical" href="https://render.jotform.com/233183619631457" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=1" />
<meta name="HandheldFriendly" content="true" />
<title>NotAtFault Claims Form</title>
<link href="https://cdn.jotfor.ms/s/static/5d16c61859f/static/formCss.css" rel="stylesheet" type="text/css" />
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/stylebuilder/static/form-common.css?v=87bd99f
"/>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/css/styles/nova.css?3.3.64126" />
<style type="text/css">@media print{*{-webkit-print-color-adjust: exact !important;color-adjust: exact !important;}.form-section{display:inline!important}.form-pagebreak{display:none!important}.form-section-closed{height:auto!important}.page-section{position:initial!important}}</style>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/css/styles/payment/payment_feature.css?3.3.64126" />
<style type="text/css">
    .form-label-left{
        width:150px;
    }
    .form-line{
        padding-top:12px;
        padding-bottom:12px;
    }
    .form-label-right{
        width:150px;
    }
    body, html{
        margin:0;
        padding:0;
        background:#fff;
    }

    .form-all{
        margin:0px auto;
        padding-top:20px;
        width:690px;
        color:#555 !important;
        font-family:"Lucida Grande", "Lucida Sans Unicode", "Lucida Sans", Verdana, sans-serif;
        font-size:14px;
    }
    .form-radio-item label, .form-checkbox-item label, .form-grading-label, .form-header{
        color: false;
    }

</style>

<style type="text/css" id="form-designer-style">
    /* Injected CSS Code */
.form-label.form-label-auto { display: block; float: none; text-align: left; width: inherit; } /*PREFERENCES STYLE*/
    .form-all {
      font-family: Lucida Grande, sans-serif;
    }
  
    .form-label.form-label-auto {
      
    display: block;
    float: none;
    text-align: left;
    width: 100%;
  
    }
  
    .form-line {
      margin-top: 12px;
      margin-bottom: 12px;
      padding-top: 0;
      padding-bottom: 0;
    }
  
    .form-all {
      max-width: 690px;
      width: 100%;
    }
  
    .form-label.form-label-left,
    .form-label.form-label-right,
    .form-label.form-label-left.form-label-auto,
    .form-label.form-label-right.form-label-auto {
      width: 150px;
    }
  
    .form-all {
      font-size: 14px
    }
  
    .supernova .form-all, .form-all {
      background-color: #fff;
    }
  
    .form-all {
      color: #555;
    }
    .form-header-group .form-header {
      color: #555;
    }
    .form-header-group .form-subHeader {
      color: #555;
    }
    .form-label-top,
    .form-label-left,
    .form-label-right,
    .form-html,
    .form-checkbox-item label,
    .form-radio-item label,
    span.FITB .qb-checkbox-label,
    span.FITB .qb-radiobox-label,
    span.FITB .form-radio label,
    span.FITB .form-checkbox label,
    [data-blotid][data-type=checkbox] [data-labelid],
    [data-blotid][data-type=radiobox] [data-labelid],
    span.FITB-inptCont[data-type=checkbox] label,
    span.FITB-inptCont[data-type=radiobox] label {
      color: #555;
    }
    .form-sub-label {
      color: #6f6f6f;
    }
  
  .supernova {
    background-color: #f5f5f5;
  }
  .supernova body {
    background: transparent;
  }
  
    .form-textbox,
    .form-textarea,
    .form-dropdown,
    .form-radio-other-input,
    .form-checkbox-other-input,
    .form-captcha input,
    .form-spinner input {
      background-color: #fff;
    }
  
      
    .supernova {
      background-repeat: no-repeat;
      background-size: cover;
      background-attachment: fixed;
      background-position: center top;
    }

      .supernova, #stage {
        background-image: none;
      }
    
      .form-all {
        background-image: none;
      }
    /*PREFERENCES STYLE*//*__INSPECT_SEPERATOR__*/
    /* Injected CSS Code */
</style>

<script>window.enableEventObserver=true</script>
<script src="https://cdn.jotfor.ms/s/static/5d16c61859f/static/prototype.forms.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/5d16c61859f/static/jotform.forms.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/5d16c61859f/js/vendor/maskedinput_5.0.9.min.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/umd/ca8f0c1af30/for-widgets-server.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/umd/ca8f0c1af30/for-sign-form-integration.js" type="text/javascript" defer></script>
<script src="https://cdn.jotfor.ms/s/umd/ca8f0c1af30/for-form-branding-footer.js" type="text/javascript" defer></script>
<script src="https://cdn.jotfor.ms/s/umd/ca8f0c1af30/for-sanitize.js" type="text/javascript"></script>
<script type="text/javascript">	JotForm.newDefaultTheme = false;
	JotForm.extendsNewTheme = false;
	JotForm.CDN_VENDOR_PATH = "https://cdn.jotfor.ms/s/vendor/static";
	JotForm.singleProduct = false;
	JotForm.newPaymentUIForNewCreatedForms = false;
	JotForm.texts = {"confirmEmail":"E-mail does not match","pleaseWait":"Please wait...","validateEmail":"You need to validate this e-mail","confirmClearForm":"Are you sure you want to clear the form","lessThan":"Your score should be less than or equal to","incompleteFields":"There are incomplete required fields. Please complete them.","required":"This field is required.","requireOne":"At least one field required.","requireEveryRow":"Every row is required.","requireEveryCell":"Every cell is required.","email":"Enter a valid e-mail address","alphabetic":"This field can only contain letters","numeric":"This field can only contain numeric values","alphanumeric":"This field can only contain letters and numbers.","cyrillic":"This field can only contain cyrillic characters","url":"This field can only contain a valid URL","currency":"This field can only contain currency values.","fillMask":"Field value must fill mask.","uploadExtensions":"You can only upload following files:","noUploadExtensions":"File has no extension file type (e.g. .txt, .png, .jpeg)","uploadFilesize":"File size cannot be bigger than:","uploadFilesizemin":"File size cannot be smaller than:","gradingScoreError":"Score total should only be less than or equal to","inputCarretErrorA":"Input should not be less than the minimum value:","inputCarretErrorB":"Input should not be greater than the maximum value:","maxDigitsError":"The maximum digits allowed is","minCharactersError":"The number of characters should not be less than the minimum value:","maxCharactersError":"The number of characters should not be more than the maximum value:","freeEmailError":"Free email accounts are not allowed","minSelectionsError":"The minimum required number of selections is ","maxSelectionsError":"The maximum number of selections allowed is ","pastDatesDisallowed":"Date must not be in the past.","dateLimited":"This date is unavailable.","dateInvalid":"This date is not valid. The date format is {format}","dateInvalidSeparate":"This date is not valid. Enter a valid {element}.","ageVerificationError":"You must be older than {minAge} years old to submit this form.","multipleFileUploads_typeError":"{file} has invalid extension. Only {extensions} are allowed.","multipleFileUploads_sizeError":"{file} is too large, maximum file size is {sizeLimit}.","multipleFileUploads_minSizeError":"{file} is too small, minimum file size is {minSizeLimit}.","multipleFileUploads_emptyError":"{file} is empty, please select files again without it.","multipleFileUploads_uploadFailed":"File upload failed, please remove it and upload the file again.","multipleFileUploads_onLeave":"The files are being uploaded, if you leave now the upload will be cancelled.","multipleFileUploads_fileLimitError":"Only {fileLimit} file uploads allowed.","dragAndDropFilesHere_infoMessage":"Drag and drop files here","chooseAFile_infoMessage":"Choose a file","maxFileSize_infoMessage":"Max. file size","generalError":"There are errors on the form. Please fix them before continuing.","generalPageError":"There are errors on this page. Please fix them before continuing.","wordLimitError":"Too many words. The limit is","wordMinLimitError":"Too few words.  The minimum is","characterLimitError":"Too many Characters.  The limit is","characterMinLimitError":"Too few characters. The minimum is","ccInvalidNumber":"Credit Card Number is invalid.","ccInvalidCVC":"CVC number is invalid.","ccInvalidExpireDate":"Expire date is invalid.","ccInvalidExpireMonth":"Expiration month is invalid.","ccInvalidExpireYear":"Expiration year is invalid.","ccMissingDetails":"Please fill up the credit card details.","ccMissingProduct":"Please select at least one product.","ccMissingDonation":"Please enter numeric values for donation amount.","disallowDecimals":"Please enter a whole number.","restrictedDomain":"This domain is not allowed","ccDonationMinLimitError":"Minimum amount is {minAmount} {currency}","requiredLegend":"All fields marked with * are required and must be filled.","geoPermissionTitle":"Permission Denied","geoPermissionDesc":"Check your browser's privacy settings.","geoNotAvailableTitle":"Position Unavailable","geoNotAvailableDesc":"Location provider not available. Please enter the address manually.","geoTimeoutTitle":"Timeout","geoTimeoutDesc":"Please check your internet connection and try again.","selectedTime":"Selected Time","formerSelectedTime":"Former Time","cancelAppointment":"Cancel Appointment","cancelSelection":"Cancel Selection","confirmSelection":"Confirm Selection","noSlotsAvailable":"No slots available","slotUnavailable":"{time} on {date} has been selected is unavailable. Please select another slot.","multipleError":"There are {count} errors on this page. Please correct them before moving on.","oneError":"There is {count} error on this page. Please correct it before moving on.","doneMessage":"Well done! All errors are fixed.","invalidTime":"Enter a valid time","doneButton":"Done","reviewSubmitText":"Review and Submit","nextButtonText":"Next","prevButtonText":"Previous","seeErrorsButton":"See Errors","notEnoughStock":"Not enough stock for the current selection","notEnoughStock_remainedItems":"Not enough stock for the current selection ({count} items left)","soldOut":"Sold Out","justSoldOut":"Just Sold Out","selectionSoldOut":"Selection Sold Out","subProductItemsLeft":"({count} items left)","startButtonText":"START","submitButtonText":"Submit","submissionLimit":"Sorry! Only one entry is allowed. <br> Multiple submissions are disabled for this form.","reviewBackText":"Back to Form","seeAllText":"See All","progressMiddleText":"of","fieldError":"field has an error.","error":"Error"};
	JotForm.useJotformSign = "Yes";
	JotForm.isFormViewTrackingAllowed = true;
	JotForm.replaceTagTest = true;
	JotForm.activeRedirect = "thanktext";
	JotForm.uploadServerURL = "https://upload.jotform.com/upload";

   JotForm.setCalculations([{"decimalPlaces":"2","defaultValue":"","equation":"{undefined}","ignoreHiddenFields":"","insertAsText":"1","isLabel":"","newCalculationType":"1","readOnly":"","replaceText":"shorttext-5","resultField":"80","showBeforeInput":"","tagReplacement":"1","useCommasForDecimals":""},{"decimalPlaces":"2","defaultValue":"","equation":"{undefined}","ignoreHiddenFields":"","insertAsText":"1","isLabel":"","newCalculationType":"1","readOnly":"","replaceText":"firstname-3","resultField":"80","showBeforeInput":"","tagReplacement":"1","useCommasForDecimals":""},{"decimalPlaces":"2","defaultValue":"","equation":"{undefined}","ignoreHiddenFields":"","insertAsText":"1","isLabel":"","newCalculationType":"1","readOnly":"","replaceText":"lastname-3","resultField":"80","showBeforeInput":"","tagReplacement":"1","useCommasForDecimals":""},{"decimalPlaces":"2","defaultValue":"","equation":"{undefined}","ignoreHiddenFields":"","insertAsText":"1","isLabel":"","newCalculationType":"1","readOnly":"","replaceText":"date-4","resultField":"80","showBeforeInput":"","tagReplacement":"1","useCommasForDecimals":""}]);	JotForm.clearFieldOnHide="disable";
	JotForm.submitError="jumpToFirstError";
	window.addEventListener('DOMContentLoaded',function(){window.brandingFooter.init({"formID":233183619631457,"campaign":"powered_by_jotform_le","isCardForm":false,"isLegacyForm":true,"formLanguage":"en"})});
	JotForm.init(function(){
	/*INIT-START*/

 JotForm.setCalendar("80-date-4");

if (window.JotForm && JotForm.accessible) $('input_58').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_51').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_59').setAttribute('tabindex',0);

 JotForm.calendarMonths = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewMonths) JotForm.calenderViewMonths = {};  JotForm.calenderViewMonths[89] = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewDays) JotForm.calenderViewDays = {};  JotForm.calenderViewDays[89] = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarDays = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarOther = {"today":"Today"};
 var languageOptions = document.querySelectorAll('#langList li'); 
 for(var langIndex = 0; langIndex < languageOptions.length; langIndex++) { 
   languageOptions[langIndex].on('click', function(e) { setTimeout(function(){ JotForm.setCalendar("89", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); }, 0); });
 } 
 JotForm.onTranslationsFetch(function() { JotForm.setCalendar("89", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); });
 JotForm.formatDate({date:(new Date()), dateField:$("id_"+89)});
if (window.JotForm && JotForm.accessible) $('input_84').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_85').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_86').setAttribute('tabindex',0);

 JotForm.calendarMonths = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewMonths) JotForm.calenderViewMonths = {};  JotForm.calenderViewMonths[88] = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewDays) JotForm.calenderViewDays = {};  JotForm.calenderViewDays[88] = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarDays = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarOther = {"today":"Today"};
 var languageOptions = document.querySelectorAll('#langList li'); 
 for(var langIndex = 0; langIndex < languageOptions.length; langIndex++) { 
   languageOptions[langIndex].on('click', function(e) { setTimeout(function(){ JotForm.setCalendar("88", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); }, 0); });
 } 
 JotForm.onTranslationsFetch(function() { JotForm.setCalendar("88", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); });
 JotForm.formatDate({date:(new Date()), dateField:$("id_"+88)});
      JotForm.alterTexts(undefined);
	/*INIT-END*/
	});

   setTimeout(function() {
JotForm.paymentExtrasOnTheFly([null,null,{"name":"submit2","qid":"2","text":"Submit","type":"control_button"},null,null,null,null,null,null,null,null,null,null,null,{"name":"typeA","qid":"14","text":"Signature","type":"control_widget"},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,{"description":"","name":"notAt","qid":"41","text":"Not At Fault Party","type":"control_fullname"},null,null,null,null,null,null,null,null,null,{"description":"","name":"insuranceCompany","qid":"51","subLabel":"","text":"Insurance Company","type":"control_textbox"},null,null,null,null,null,null,{"description":"","name":"claimNumber58","qid":"58","subLabel":"Vehicle Details","text":"Rego No.","type":"control_textbox"},{"description":"","name":"claimNumber59","qid":"59","subLabel":"The claim number provided by your insurer","text":"Claim Number","type":"control_textbox"},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,{"description":"","name":"input80","qid":"80","template":"WHITE POINTER RECOVERIES PTY LTD\u003Cbr\u002F\u003EABN **************\u003Cbr\u002F\u003EAUTHORITY TO ACT, DIRECTION TO PAY AND DEED OF SUBROGATION\u003Cbr\u002F\u003E(CREDIT HIRE ONLY)\u003Cbr\u002F\u003E\u003Cbr\u002F\u003ECC REF No.   {shorttext-5}   \u003Cbr\u002F\u003E\u003Cbr\u002F\u003E1, I   {firstname-3}   {lastname-3}   , have suffered loss as a result of the collision I was involved in on   {date-4}   .  \u003Cbr\u002F\u003EI was not at fault in this collision\u003Cbr\u002F\u003EI am the owner of the vehicle.\u003Cbr\u002F\u003EI irrevocably appoint White Pointer to act on my behalf to recover the loss associated with the replacement vehicle and any miscellaneous loss defined by clause 14(d) below (“miscellaneous loss”). I also understand that White Pointer will instruct MGL Lawyers or another lawyer appointed by White Pointer to act on my behalf to recover the loss associated with the replacement vehicle and &#x2F; or any miscellaneous loss. This appointment does not cover the loss associated with other loss I have suffered including damage and repairs to my vehicle. I am responsible for pursuing these claims, although I understand that the proceedings for the damage to my vehicle must be brought together with the rental claim, else I may be unable to bring the claim for damage and repairs later. I confirm I will&#x2F;have provided White Pointer with\u003Cbr\u002F\u003Eall required documents requested to process the claim. By signing this Deed, I confirm I have read, understood and consent to be bound by the clauses outlined below. I declare that the particulars I have supplied in relation to the collision are true and correct to the best of my knowledge and belief\u003Cbr\u002F\u003E \u003Cbr\u002F\u003EGENERAL\u003Cbr\u002F\u003EAUTHORITY TO ACT, DIRECTION TO PAY AND DEED OF SUBROGATION\u003Cbr\u002F\u003E1. I understand that I have suffered loss as a result of the negligence&#x2F;fault of a third party because of the Accident, as I have identified in the form above.I was not at fault regarding this Accident.3. I wish to appoint White Pointer to recover compensation for the losses associated with the repair of my vehicle, and other associated costs replacement vehicle &#x2F; miscellaneous loss.\u003Cbr\u002F\u003ECOOPERATION\u003Cbr\u002F\u003E4. I understand that I must provide full cooperation as required in this agreement to assist with enquiries regarding my claim.\u003Cbr\u002F\u003E5. I conﬁrm I will follow and fully cooperate with all reasonable requests in a timely manner and to the best of my ability.\u003Cbr\u002F\u003E6. If I am responsible for the conduct of my claim regarding repairs, towing, demurrage and assessment, I acknowledge it is likely that this claim will need to be brought in the same proceedings as the proceedings for the market cost of the replacement vehicle, and that I will do everything necessary to cooperate with White Pointer in the commencement, continuation and settlement of the claim.\u003Cbr\u002F\u003E7. Under this deed, I conﬁrm I will:\u003Cbr\u002F\u003Ea) Be truthful in all information I provide.\u003Cbr\u002F\u003Eb) Provide information or documents at the request of White Pointer or any other nominated legal representative.\u003Cbr\u002F\u003Ec) Review and provide a signed statement or afﬁdavit, if necessary.\u003Cbr\u002F\u003Ed) Attend court as a witness when required.\u003Cbr\u002F\u003E8. Under this deed, I conﬁrm I will NOT:\u003Cbr\u002F\u003Ea) Make any admission of liability in respect to the accident to any person or business;\u003Cbr\u002F\u003Eb) Release, by signing a form or otherwise, the other party from compensating the loss (with respect to the replacement vehicle and &#x2F; or miscellaneous loss) suffered; or\u003Cbr\u002F\u003Ec) Negotiate with any insurance company, person, business, lawyer, or related or interested\u003Cbr\u002F\u003Eentity regarding the settlement ﬁgure to be awarded to me for compensation for my loss. (with respect to the replacement vehicle and &#x2F;\u003Cbr\u002F\u003Eor miscellaneous loss).\u003Cbr\u002F\u003ESERVICES ORGANISED ON MY BEHALF\u003Cbr\u002F\u003E9. I authorise White Pointer to organise the following on my behalf in relation to the Accident;\u003Cbr\u002F\u003Ea. Recover the costs associated with my replacement vehicle;\u003Cbr\u002F\u003Eb. A lawyer to do anything necessary to recover my losses with respect to thereplacement vehicle from the at fault party or their insurer.\u003Cbr\u002F\u003E10.  I will enter into an agreement with the people or companies who provide replacement vehicles and miscellaneous items including but not limited to helmets or gloves. This means that I wil not have to pay anything upfront while my vehicle is repaired, and I will only be liable when money is recovered from the at-fault party or their insurer.\u003Cbr\u002F\u003E11. I understand that White Pointer may charge a fee to third parties engaged by White Pointer to assist in providing me with services under this agreement.\u003Cbr\u002F\u003E12.   I understand that White Pointer will enter into a costs agreement with the lawyer retained on my behalf, and will receive the relevant disclosure as a third party payer.\u003Cbr\u002F\u003E13.    I understand that this Deed is not an insurance contract, as no insurance premiums are paid to White Pointer by me.\u003Cbr\u002F\u003ERECOVERY OF LOSS\u003Cbr\u002F\u003E14.    I understand that my losses resulting from the Accident may include;\u003Cbr\u002F\u003Ea) Repair costs;\u003Cbr\u002F\u003Eb) Rental costs based on a daily rate that is applied per day for the required period whilst my vehicle is being assessed and repaired;\u003Cbr\u002F\u003Ec) Towing costs;\u003Cbr\u002F\u003Ed) Miscellaneous loss including but not limited to items in my vehicle;\u003Cbr\u002F\u003Ee) Demurrage;\u003Cbr\u002F\u003Ef) Expert report&#x2F;assessment costs;\u003Cbr\u002F\u003Eg) Legal costs;\u003Cbr\u002F\u003Eh) Disbursements.\u003Cbr\u002F\u003E15.    I understand that the market cost for the hire of the replacement vehicle, along with any miscellaneous items will be recovered on my behalf by White Pointer.\u003Cbr\u002F\u003E16.   I understand that with respect to the market cost for repairs, towing, diminished value, demurrage and assessment I am responsible for conducting these proceedings (if applicable), and I understand:\u003Cbr\u002F\u003Ea.  I may, or may not engage MGL Lawyers or the same authorised legal representative to conduct these proceedings on my behalf;\u003Cbr\u002F\u003Eb.   I retain my rights to conduct the proceedings mentioned in clause 16 above and in these proceedings:\u003Cbr\u002F\u003Ei.  Assignments mentioned at clause 18, 19 and 20 do not apply.\u003Cbr\u002F\u003Eii.  Indemnities mentioned at clause 31 continue to apply.\u003Cbr\u002F\u003Ec.  It is likely these proceedings mentioned in this paragraph must be commenced and concluded together with the proceedings for the market cost of the hire of the replacement vehicle and miscellaneous items (clause 14 above) else I may lose any rights I have under these claims.\u003Cbr\u002F\u003E17.    I understand that this Deed is not subject to the National Credit Protection Act or the National Credit Code.\u003Cbr\u002F\u003EI understand that with respect to the market cost for repairs, towing, diminished value, demurrage and assessment I am responsible for conducting these proceedings (if applicable), and I understand:\u003Cbr\u002F\u003ELITIGATION\u003Cbr\u002F\u003E18.   I understand if there is a dispute as to the reasonable amount of my loss, a law firm named MGL Lawyers or another nominated law firm will be instructed to commence legal proceedings in my name and on my behalf against the at-fault party.\u003Cbr\u002F\u003E19.   I authorise MGL Lawyers and&#x2F;or any other nominated legal representative to do the following on my behalf:\u003Cbr\u002F\u003Ea) Make all reasonable enquiries relating to my loss to implicated or interested parties including, but without limitation to, the third-party and&#x2F;or the third-party insurer;\u003Cbr\u002F\u003Eb) File and conduct legal proceedings on my behalf and in my name;\u003Cbr\u002F\u003Ec) Acquire relevant information and documents from third parties;\u003Cbr\u002F\u003Ed) Use the personal information I provided to White Pointer either verbally, in writing and&#x2F;or set out in the White Pointer Claim Form to assist in recovering my loss;\u003Cbr\u002F\u003Ee) Prepare documents on my behalf including, but not limited to: statements, afﬁdavits or any other evidence relevant to, and required for, the purposes of litigation or the settlement of my claim; and\u003Cbr\u002F\u003Ef) Release other parties from liability on my behalf by way of deed or consent orders.\u003Cbr\u002F\u003E20.    I agree to sign all documents and do anything else that is necessary for MGL Lawyers and&#x2F;or any other nominated legal representative to do those things.\u003Cbr\u002F\u003EAUTHORITY TO RECEIVE AND PAY\u003Cbr\u002F\u003E21.   I understand that for the recovery of my loss there will be requests made for payment from the at fault party and or their insurer.\u003Cbr\u002F\u003E22.   I understand that as part of procedure of authorising White Pointer or any nominated legal representative to act on my behalf to recover any loss due to the accident, White Pointer or any nominated legal representative will receive a settlement cheque or payment in my name when the claim or matter has been ﬁnalised.\u003Cbr\u002F\u003E23.    I hereby authorise White Pointer and or the nominated legal representative to receive any settlement monies to be paid to me by way of verdict, award or agreement.\u003Cbr\u002F\u003E24.   I further authorise White Pointer or any nominated legal representative to bank relevant payments made out in my name into an account nominated by White Pointer.\u003Cbr\u002F\u003E25.   I conﬁrm that should I receive any payment from an insurer or third party directly related to this claim I will forward the entire payment to White Pointer within 48 hours by cheque or EFT.\u003Cbr\u002F\u003E26.  I authorise White Pointer and&#x2F;or MGL Lawyers and&#x2F;or any other nominated legal representative to pay from the nominated account any monies on my behalf to White Pointer, MGL Lawyers, or any other person or business who is owed money arising from the claim or matter. Such persons or businesses include, without limitationt,he legal representatives nominated to represent me, and where applicable, the hire car company that provided a replacement vehicle or loaned me any miscellaneous items whilst my vehicle was beingrepaired.\u003Cbr\u002F\u003ETERMINATION OF THIS DEED\u003Cbr\u002F\u003E27.    I understand and acknowledge this Deed is irrevocable by me.\u003Cbr\u002F\u003E28.   However, I acknowledge that White Pointer reserves the right to terminate this deed with immediate effect at its absolute discretion, without notice and for any reason.\u003Cbr\u002F\u003E29.   I understand if I breach the terms of this Deed or attempt to terminate this Deed White Pointer at its sole discretion may cease all dealings with me. I understand should this occur, that the indemnities at the whole of clause 31 shall cease to apply and I am personally liable for all costs associated with my loss and any recovery costs accrued whilst White Pointer and any nominated legal representative acted on my behalf.\u003Cbr\u002F\u003E30.    Any termination of this Deed shall be without prejudice to any rights which either party may have against the other arising out of or connection with this Deed.\u003Cbr\u002F\u003EINDEMNITY\u003Cbr\u002F\u003E31.   a) I understand that White Pointer, in reliance on the warranties and covenants provided by me and subject to the terms and conditions outlined in this Deed, agrees to indemnify me in respect of the cost of the replacement vehicle and loan of any miscellaneous items incurred on my behalf, as a loss arising from the Accident\u003Cbr\u002F\u003Eb) I understand that White Pointer, in reliance on the warranties and covenants provided by me and subject to the terms and conditions outlined in this Deed, agrees to indemnify me in respect of the whole of the legal costs incurred on my behalf, and any adverse costs order that may be made against me in legal proceedings commenced to recover my loss arising from the Accident,\u003Cbr\u002F\u003Ewhether or not these costs relate to the claim regarding the replacement vehicle, or another claim arising from the Accident.\u003Cbr\u002F\u003Ec) I agree that the indemnity will not apply and will cease to have effect in circumstances where:\u003Cbr\u002F\u003Ei.    I breach any of the essential terms of this Deed;\u003Cbr\u002F\u003Eii.   I deal directly with the at-fault party or their insurer without the consent of White Pointer;\u003Cbr\u002F\u003Eiii. I refuse or fail to respond to reasonable requests from White Pointer or any nominated legal representatives for information or documents reasonably necessary to the proper prosecution or settlement of my claim;\u003Cbr\u002F\u003Eiv. I refuse to accept reasonable advice rendered by MGL Lawyers or the other legal representative nominated by White Pointer:\u003Cbr\u002F\u003Ev.  I give false or fraudulent information to White Pointer, the Defendant, the Insurer, or the court.\u003Cbr\u002F\u003Evi. I purport to withdraw my authority and instructions to act on my behalf (even though I am not permitted to do so under this Deed).\u003Cbr\u002F\u003Ed) If the indemnity ceases to have effect then, unless otherwise agreed, I will become immediately liable to White Pointer for any liability or expense incurred by White Pointer because of this Deed.","text":"","type":"control_inline"},null,null,{"description":"","name":"atFault","qid":"83","text":"At Fault Party","type":"control_fullname"},{"description":"","name":"regoNo","qid":"84","subLabel":"Vehicle Details","text":"Rego No.","type":"control_textbox"},{"description":"","name":"insuranceCompany85","qid":"85","subLabel":"","text":"Insurance Company","type":"control_textbox"},{"description":"","name":"claimNumber","qid":"86","subLabel":"The claim number provided by your insurer","text":"Claim Number","type":"control_textbox"},{"name":"signature","qid":"87","text":"Signature","type":"control_widget"},{"description":"","name":"date","qid":"88","text":"Date","type":"control_datetime"},{"description":"","name":"date89","qid":"89","text":"Date","type":"control_datetime"}]);}, 20); 
</script>
</head>
<body>
<form class="jotform-form" onsubmit="return typeof testSubmitFunction !== 'undefined' && testSubmitFunction();" action="https://submit.jotform.com/submit/233183619631457" method="post" name="form_233183619631457" id="233183619631457" accept-charset="utf-8" autocomplete="on"><input type="hidden" name="formID" value="233183619631457" /><input type="hidden" id="JWTContainer" value="" /><input type="hidden" id="cardinalOrderNumber" value="" /><input type="hidden" id="jsExecutionTracker" name="jsExecutionTracker" value="build-date-1753169867286" /><input type="hidden" id="submitSource" name="submitSource" value="unknown" /><input type="hidden" id="submitDate" name="submitDate" value="undefined" /><input type="hidden" id="buildDate" name="buildDate" value="1753169867286" /><input type="hidden" name="uploadServerUrl" value="https://upload.jotform.com/upload" /><input type="hidden" name="eventObserver" value="1" />
  <div role="main" class="form-all">
    <ul class="form-section page-section" role="presentation">
      <li class="form-line" data-type="control_inline" id="id_80">
        <div id="cid_80" class="form-input-wide">
          <div id="FITB_80" class="FITB formRender">
            <p>WHITE POINTER RECOVERIES PTY LTD<br />ABN **************<br />AUTHORITY TO ACT, DIRECTION TO PAY AND DEED OF SUBROGATION<br />(CREDIT HIRE ONLY)<br /><br />CC REF No.   <span data-type="textbox" data-grouptype="control_textbox" class="FITB-inptCont" data-blot-id="shorttext-5"><input class="form-textbox  validate[]" name="q80_input80[shorttext-5]" id="80_shorttext-5" type="text" /><label for="80_shorttext-5"></label></span>   <br /><br />1, I   <span data-type="textbox" data-grouptype="control_fullname" class="FITB-inptCont" data-blot-id="firstname-3"><input class="form-textbox  validate[]" name="q80_input80[firstname-3]" id="80_firstname-3" type="text" /><label for="80_firstname-3">First Name</label></span>   <span data-type="textbox" data-grouptype="control_fullname" class="FITB-inptCont" data-blot-id="lastname-3"><input class="form-textbox  validate[]" name="q80_input80[lastname-3]" id="80_lastname-3" type="text" /><label for="80_lastname-3">Last Name</label></span>   , have suffered loss as a result of the collision I was involved in on   <span id="id_80-date-4" data-type="datebox" class="FITB-inptCont" data-blot-id="date-4"><input class="form-textbox validate[validateLiteDate]" id="lite_mode_80-date-4" type="text" data-format="ddmmyyyy" size="12" data-seperator="-" placeholder="dd-mm-yyyy" /><img class="newDefaultTheme-dateIcon icon-liteMode" alt="Pick a Date" data-qtype="control_inline" id="input_80-date-4_pick" src="https://cdn.jotfor.ms/images/calendar.png" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v1" /><label for="lite_mode_80-date-4"></label><span style="display:none"><input type="tel" class="form-textbox" id="month_80-date-4" name="q80_input80[date-4][month]" size="2" data-maxlength="2" maxlength="2" autocomplete="off" /><input type="tel" class="form-textbox" id="day_80-date-4" name="q80_input80[date-4][day]" size="2" data-maxlength="2" maxlength="2" autocomplete="off" /><input type="tel" class="form-textbox" id="year_80-date-4" name="q80_input80[date-4][year]" size="4" data-maxlength="4" maxlength="4" autocomplete="off" /></span></span>   . <span style="color:#000000"> </span><br /><span style="color:#000000">I was not at fault in this collision</span><br />I am the owner of the vehicle.<br />I irrevocably appoint White Pointer to act on my behalf to recover the loss associated with the replacement vehicle and any miscellaneous loss defined by clause 14(d) below (“miscellaneous loss”). I also understand that White Pointer will instruct MGL Lawyers or another lawyer appointed by White Pointer to act on my behalf to recover the loss associated with the replacement vehicle and / or any miscellaneous loss. This appointment does not cover the loss associated with other loss I have suffered including damage and repairs to my vehicle. I am responsible for pursuing these claims, although I understand that the proceedings for the damage to my vehicle must be brought together with the rental claim, else I may be unable to bring the claim for damage and repairs later. I confirm I will/have provided White Pointer with<br />all required documents requested to process the claim. By signing this Deed, I confirm I have read, understood and consent to be bound by the clauses outlined below. I declare that the particulars I have supplied in relation to the collision are true and correct to the best of my knowledge and belief<br /> <br />GENERAL<br />AUTHORITY TO ACT, DIRECTION TO PAY AND DEED OF SUBROGATION<br />1. I understand that I have suffered loss as a result of the negligence/fault of a third party because of the Accident, as I have identified in the form above.</p>
            <ol>
              <li>I was not at fault regarding this Accident.</li>
            </ol>
            <p>3. I wish to appoint White Pointer to recover compensation for the losses associated with the repair of my vehicle, and other associated costs replacement vehicle / miscellaneous loss.<br />COOPERATION<br />4. I understand that I must provide full cooperation as required in this agreement to assist with enquiries regarding my claim.<br />5. I conﬁrm I will follow and fully cooperate with all reasonable requests in a timely manner and to the best of my ability.<br />6. If I am responsible for the conduct of my claim regarding repairs, towing, demurrage and assessment, I acknowledge it is likely that this claim will need to be brought in the same proceedings as the proceedings for the market cost of the replacement vehicle, and that I will do everything necessary to cooperate with White Pointer in the commencement, continuation and settlement of the claim.<br />7. Under this deed, I conﬁrm I will:<br />a) Be truthful in all information I provide.<br />b) Provide information or documents at the request of White Pointer or any other nominated legal representative.<br />c) Review and provide a signed statement or afﬁdavit, if necessary.<br />d) Attend court as a witness when required.<br />8. Under this deed, I conﬁrm I will NOT:<br />a) Make any admission of liability in respect to the accident to any person or business;<br />b) Release, by signing a form or otherwise, the other party from compensating the loss (with respect to the replacement vehicle and / or miscellaneous loss) suffered; or<br />c) Negotiate with any insurance company, person, business, lawyer, or related or interested<br />entity regarding the settlement ﬁgure to be awarded to me for compensation for my loss. (with respect to the replacement vehicle and /<br />or miscellaneous loss).<br />SERVICES ORGANISED ON MY BEHALF<br />9. I authorise White Pointer to organise the following on my behalf in relation to the Accident;<br />a. Recover the costs associated with my replacement vehicle;<br />b. A lawyer to do anything necessary to recover my losses with respect to thereplacement vehicle from the at fault party or their insurer.<br />10.  I will enter into an agreement with the people or companies who provide replacement vehicles and miscellaneous items including but not limited to helmets or gloves. This means that I wil not have to pay anything upfront while my vehicle is repaired, and I will only be liable when money is recovered from the at-fault party or their insurer.<br />11. I understand that White Pointer may charge a fee to third parties engaged by White Pointer to assist in providing me with services under this agreement.<br />12.   I understand that White Pointer will enter into a costs agreement with the lawyer retained on my behalf, and will receive the relevant disclosure as a third party payer.<br />13.    I understand that this Deed is not an insurance contract, as no insurance premiums are paid to White Pointer by me.<br />RECOVERY OF LOSS<br />14.    I understand that my losses resulting from the Accident may include;<br />a) Repair costs;<br />b) Rental costs based on a daily rate that is applied per day for the required period whilst my vehicle is being assessed and repaired;<br />c) Towing costs;<br />d) Miscellaneous loss including but not limited to items in my vehicle;<br />e) Demurrage;<br />f) Expert report/assessment costs;<br />g) Legal costs;<br />h) Disbursements.<br />15.    I understand that the market cost for the hire of the replacement vehicle, along with any miscellaneous items will be recovered on my behalf by White Pointer.<br />16.   I understand that with respect to the market cost for repairs, towing, diminished value, demurrage and assessment I am responsible for conducting these proceedings (if applicable), and I understand:<br />a.  I may, or may not engage MGL Lawyers or the same authorised legal representative to conduct these proceedings on my behalf;<br />b.   I retain my rights to conduct the proceedings mentioned in clause 16 above and in these proceedings:<br />i.  Assignments mentioned at clause 18, 19 and 20 do not apply.<br />ii.  Indemnities mentioned at clause 31 continue to apply.<br />c.  It is likely these proceedings mentioned in this paragraph must be commenced and concluded together with the proceedings for the market cost of the hire of the replacement vehicle and miscellaneous items (clause 14 above) else I may lose any rights I have under these claims.<br />17.    I understand that this Deed is not subject to the National Credit Protection Act or the National Credit Code.<br />I understand that with respect to the market cost for repairs, towing, diminished value, demurrage and assessment I am responsible for conducting these proceedings (if applicable), and I understand:<br />LITIGATION<br />18.   I understand if there is a dispute as to the reasonable amount of my loss, a law firm named MGL Lawyers or another nominated law firm will be instructed to commence legal proceedings in my name and on my behalf against the at-fault party.<br />19.   I authorise MGL Lawyers and/or any other nominated legal representative to do the following on my behalf:<br />a) Make all reasonable enquiries relating to my loss to implicated or interested parties including, but without limitation to, the third-party and/or the third-party insurer;<br />b) File and conduct legal proceedings on my behalf and in my name;<br />c) Acquire relevant information and documents from third parties;<br />d) Use the personal information I provided to White Pointer either verbally, in writing and/or set out in the White Pointer Claim Form to assist in recovering my loss;<br />e) Prepare documents on my behalf including, but not limited to: statements, afﬁdavits or any other evidence relevant to, and required for, the purposes of litigation or the settlement of my claim; and<br />f) Release other parties from liability on my behalf by way of deed or consent orders.<br />20.    I agree to sign all documents and do anything else that is necessary for MGL Lawyers and/or any other nominated legal representative to do those things.<br />AUTHORITY TO RECEIVE AND PAY<br />21.   I understand that for the recovery of my loss there will be requests made for payment from the at fault party and or their insurer.<br />22.   I understand that as part of procedure of authorising White Pointer or any nominated legal representative to act on my behalf to recover any loss due to the accident, White Pointer or any nominated legal representative will receive a settlement cheque or payment in my name when the claim or matter has been ﬁnalised.<br />23.    I hereby authorise White Pointer and or the nominated legal representative to receive any settlement monies to be paid to me by way of verdict, award or agreement.<br />24.   I further authorise White Pointer or any nominated legal representative to bank relevant payments made out in my name into an account nominated by White Pointer.<br />25.   I conﬁrm that should I receive any payment from an insurer or third party directly related to this claim I will forward the entire payment to White Pointer within 48 hours by cheque or EFT.<br />26.  I authorise White Pointer and/or MGL Lawyers and/or any other nominated legal representative to pay from the nominated account any monies on my behalf to White Pointer, MGL Lawyers, or any other person or business who is owed money arising from the claim or matter. Such persons or businesses include, without limitationt,he legal representatives nominated to represent me, and where applicable, the hire car company that provided a replacement vehicle or loaned me any miscellaneous items whilst my vehicle was beingrepaired.<br />TERMINATION OF THIS DEED<br />27.    I understand and acknowledge this Deed is irrevocable by me.<br />28.   However, I acknowledge that White Pointer reserves the right to terminate this deed with immediate effect at its absolute discretion, without notice and for any reason.<br />29.   I understand if I breach the terms of this Deed or attempt to terminate this Deed White Pointer at its sole discretion may cease all dealings with me. I understand should this occur, that the indemnities at the whole of clause 31 shall cease to apply and I am personally liable for all costs associated with my loss and any recovery costs accrued whilst White Pointer and any nominated legal representative acted on my behalf.<br />30.    Any termination of this Deed shall be without prejudice to any rights which either party may have against the other arising out of or connection with this Deed.<br />INDEMNITY<br />31.   a) I understand that White Pointer, in reliance on the warranties and covenants provided by me and subject to the terms and conditions outlined in this Deed, agrees to indemnify me in respect of the cost of the replacement vehicle and loan of any miscellaneous items incurred on my behalf, as a loss arising from the Accident<br />b) I understand that White Pointer, in reliance on the warranties and covenants provided by me and subject to the terms and conditions outlined in this Deed, agrees to indemnify me in respect of the whole of the legal costs incurred on my behalf, and any adverse costs order that may be made against me in legal proceedings commenced to recover my loss arising from the Accident,<br />whether or not these costs relate to the claim regarding the replacement vehicle, or another claim arising from the Accident.<br />c) I agree that the indemnity will not apply and will cease to have effect in circumstances where:<br />i.    I breach any of the essential terms of this Deed;<br />ii.   I deal directly with the at-fault party or their insurer without the consent of White Pointer;<br />iii. I refuse or fail to respond to reasonable requests from White Pointer or any nominated legal representatives for information or documents reasonably necessary to the proper prosecution or settlement of my claim;<br />iv. I refuse to accept reasonable advice rendered by MGL Lawyers or the other legal representative nominated by White Pointer:<br />v.  I give false or fraudulent information to White Pointer, the Defendant, the Insurer, or the court.<br />vi. I purport to withdraw my authority and instructions to act on my behalf (even though I am not permitted to do so under this Deed).<br />d) If the indemnity ceases to have effect then, unless otherwise agreed, I will become immediately liable to White Pointer for any liability or expense incurred by White Pointer because of this Deed.</p>
          </div>
        </div>
      </li>
      <li class="form-line jf-required" data-type="control_fullname" id="id_41"><label class="form-label form-label-top form-label-auto" id="label_41" for="first_41" aria-hidden="false"> Not At Fault Party<span class="form-required">*</span> </label>
        <div id="cid_41" class="form-input-wide jf-required">
          <div data-wrapper-react="true"><span class="form-sub-label-container" style="vertical-align:top" data-input-type="first"><input type="text" id="first_41" name="q41_notAt[first]" class="form-textbox validate[required]" data-defaultvalue="" autoComplete="section-input_41 given-name" size="10" data-component="first" aria-labelledby="label_41 sublabel_41_first" required="" value="" /><label class="form-sub-label" for="first_41" id="sublabel_41_first" style="min-height:13px">First Name</label></span><span class="form-sub-label-container" style="vertical-align:top" data-input-type="last"><input type="text" id="last_41" name="q41_notAt[last]" class="form-textbox validate[required]" data-defaultvalue="" autoComplete="section-input_41 family-name" size="15" data-component="last" aria-labelledby="label_41 sublabel_41_last" required="" value="" /><label class="form-sub-label" for="last_41" id="sublabel_41_last" style="min-height:13px">Last Name</label></span></div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_58"><label class="form-label form-label-top form-label-auto" id="label_58" for="input_58" aria-hidden="false"> Rego No. </label>
        <div id="cid_58" class="form-input-wide"> <span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_58" name="q58_claimNumber58" data-type="input-textbox" class="form-textbox" data-defaultvalue="" size="20" data-component="textbox" aria-labelledby="label_58 sublabel_input_58" value="" /><label class="form-sub-label" for="input_58" id="sublabel_input_58" style="min-height:13px">Vehicle Details</label></span> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_51"><label class="form-label form-label-top form-label-auto" id="label_51" for="input_51" aria-hidden="false"> Insurance Company </label>
        <div id="cid_51" class="form-input-wide"> <input type="text" id="input_51" name="q51_insuranceCompany" data-type="input-textbox" class="form-textbox" data-defaultvalue="" size="20" data-component="textbox" aria-labelledby="label_51" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_59"><label class="form-label form-label-top form-label-auto" id="label_59" for="input_59" aria-hidden="false"> Claim Number </label>
        <div id="cid_59" class="form-input-wide"> <span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_59" name="q59_claimNumber59" data-type="input-textbox" class="form-textbox" data-defaultvalue="" size="20" data-component="textbox" aria-labelledby="label_59 sublabel_input_59" value="" /><label class="form-sub-label" for="input_59" id="sublabel_input_59" style="min-height:13px">The claim number provided by your insurer</label></span> </div>
      </li>
      <li class="form-line" data-type="control_widget" id="id_14"><label class="form-label form-label-top form-label-auto" id="label_14" for="input_14" aria-hidden="false"> Signature </label>
        <div id="cid_14" class="form-input-wide">
          <div data-widget-name="Smooth Signature" style="width:100%;text-align:Left;overflow-x:auto" data-component="widget-field"><iframe data-client-id="529467003477f3512000001f" title="Smooth Signature" frameBorder="0" scrolling="no" allowtransparency="true" allow="geolocation; microphone; camera; autoplay; encrypted-media; fullscreen" data-type="iframe" class="custom-field-frame" id="customFieldFrame_14" src="" style="max-width:400px;border:none;width:100%;height:200px" data-width="400" data-height="200"></iframe>
            <div class="widget-inputs-wrapper"><input id="input_14" class="form-hidden form-widget  " type="hidden" name="q14_typeA" value="" /><input id="widget_settings_14" class="form-hidden form-widget-settings" type="hidden" data-version="2" value="%5B%5D" /></div>
            <script type="text/javascript">
              setTimeout(function()
              {
                function renderWidget()
                {
                  var _cFieldFrame = document.getElementById("customFieldFrame_14");
                  if (_cFieldFrame)
                  {
                    _cFieldFrame.onload = function()
                    {
                      if (typeof widgetFrameLoaded !== 'undefined')
                      {
                        widgetFrameLoaded(14,
                        {
                          "formID": 233183619631457
                        }, undefined)
                      }
                    };
                    _cFieldFrame.src = "//data-widgets.jotform.io/signature-pad/?qid=14&isOpenedInPortal=undefined&isOpenedInAgent=undefined&align=Left&ref=" +
                      encodeURIComponent(window.location.protocol + "//" + window.location.host) + '' + '' + '&useJotformSign=Yes' +
                      '&injectCSS=' + encodeURIComponent(window.location.search.indexOf("ndt=1") > -1);
                    _cFieldFrame.addClassName("custom-field-frame-rendered");
                  }
                }
                if (false)
                {
                  var _interval = setInterval(function()
                  {
                    var dataMode = document.querySelector('html').getAttribute('data-mode');
                    if (dataMode === 'fillMode')
                    {
                      renderWidget()
                      clearInterval(_interval);
                    }
                  }, 1000);
                }
                else
                {
                  renderWidget();
                }
              }, 0);
            </script>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_datetime" id="id_89"><label class="form-label form-label-top form-label-auto" id="label_89" for="lite_mode_89" aria-hidden="false"> Date </label>
        <div id="cid_89" class="form-input-wide">
          <div data-wrapper-react="true">
            <div style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input class="currentDate form-textbox validate[limitDate]" id="day_89" name="q89_date89[day]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_89 sublabel_89_day" value="22" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="day_89" id="sublabel_89_day" style="min-height:13px">Day</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="month_89" name="q89_date89[month]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_89 sublabel_89_month" value="07" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="month_89" id="sublabel_89_month" style="min-height:13px">Month</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="year_89" name="q89_date89[year]" type="tel" size="4" data-maxlength="4" data-age="" maxLength="4" autoComplete="off" aria-labelledby="label_89 sublabel_89_year" value="2025" /><label class="form-sub-label" for="year_89" id="sublabel_89_year" style="min-height:13px">Year</label></span></div><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate, validateLiteDate]" id="lite_mode_89" type="text" size="12" data-maxlength="12" maxLength="12" data-age="" data-format="ddmmyyyy" data-seperator="-" placeholder="dd-mm-yyyy" data-placeholder="DD-MM-YYYY" autoComplete="off" aria-labelledby="label_89 sublabel_89_litemode" value="22-07-2025" /><button type="button" class=" newDefaultTheme-dateIcon focusable icon-liteMode" id="input_89_pick" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v1"></button><label class="form-sub-label" for="lite_mode_89" id="sublabel_89_litemode" style="min-height:13px">Date</label></span>
          </div>
        </div>
      </li>
      <li class="form-line jf-required" data-type="control_fullname" id="id_83"><label class="form-label form-label-top form-label-auto" id="label_83" for="first_83" aria-hidden="false"> At Fault Party<span class="form-required">*</span> </label>
        <div id="cid_83" class="form-input-wide jf-required">
          <div data-wrapper-react="true"><span class="form-sub-label-container" style="vertical-align:top" data-input-type="first"><input type="text" id="first_83" name="q83_atFault[first]" class="form-textbox validate[required]" data-defaultvalue="" autoComplete="section-input_83 given-name" size="10" data-component="first" aria-labelledby="label_83 sublabel_83_first" required="" value="" /><label class="form-sub-label" for="first_83" id="sublabel_83_first" style="min-height:13px">First Name</label></span><span class="form-sub-label-container" style="vertical-align:top" data-input-type="last"><input type="text" id="last_83" name="q83_atFault[last]" class="form-textbox validate[required]" data-defaultvalue="" autoComplete="section-input_83 family-name" size="15" data-component="last" aria-labelledby="label_83 sublabel_83_last" required="" value="" /><label class="form-sub-label" for="last_83" id="sublabel_83_last" style="min-height:13px">Last Name</label></span></div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_84"><label class="form-label form-label-top form-label-auto" id="label_84" for="input_84" aria-hidden="false"> Rego No. </label>
        <div id="cid_84" class="form-input-wide"> <span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_84" name="q84_regoNo" data-type="input-textbox" class="form-textbox" data-defaultvalue="" size="20" data-component="textbox" aria-labelledby="label_84 sublabel_input_84" value="" /><label class="form-sub-label" for="input_84" id="sublabel_input_84" style="min-height:13px">Vehicle Details</label></span> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_85"><label class="form-label form-label-top form-label-auto" id="label_85" for="input_85" aria-hidden="false"> Insurance Company </label>
        <div id="cid_85" class="form-input-wide"> <input type="text" id="input_85" name="q85_insuranceCompany85" data-type="input-textbox" class="form-textbox" data-defaultvalue="" size="20" data-component="textbox" aria-labelledby="label_85" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_86"><label class="form-label form-label-top form-label-auto" id="label_86" for="input_86" aria-hidden="false"> Claim Number </label>
        <div id="cid_86" class="form-input-wide"> <span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_86" name="q86_claimNumber" data-type="input-textbox" class="form-textbox" data-defaultvalue="" size="20" data-component="textbox" aria-labelledby="label_86 sublabel_input_86" value="" /><label class="form-sub-label" for="input_86" id="sublabel_input_86" style="min-height:13px">The claim number provided by your insurer</label></span> </div>
      </li>
      <li class="form-line" data-type="control_widget" id="id_87"><label class="form-label form-label-top form-label-auto" id="label_87" for="input_87" aria-hidden="false"> Signature </label>
        <div id="cid_87" class="form-input-wide">
          <div data-widget-name="Smooth Signature" style="width:100%;text-align:Left;overflow-x:auto" data-component="widget-field"><iframe data-client-id="529467003477f3512000001f" title="Smooth Signature" frameBorder="0" scrolling="no" allowtransparency="true" allow="geolocation; microphone; camera; autoplay; encrypted-media; fullscreen" data-type="iframe" class="custom-field-frame" id="customFieldFrame_87" src="" style="max-width:400px;border:none;width:100%;height:200px" data-width="400" data-height="200"></iframe>
            <div class="widget-inputs-wrapper"><input id="input_87" class="form-hidden form-widget  " type="hidden" name="q87_signature" value="" /><input id="widget_settings_87" class="form-hidden form-widget-settings" type="hidden" data-version="2" value="%5B%5D" /></div>
            <script type="text/javascript">
              setTimeout(function()
              {
                function renderWidget()
                {
                  var _cFieldFrame = document.getElementById("customFieldFrame_87");
                  if (_cFieldFrame)
                  {
                    _cFieldFrame.onload = function()
                    {
                      if (typeof widgetFrameLoaded !== 'undefined')
                      {
                        widgetFrameLoaded(87,
                        {
                          "formID": 233183619631457
                        }, undefined)
                      }
                    };
                    _cFieldFrame.src = "//data-widgets.jotform.io/signature-pad/?qid=87&isOpenedInPortal=undefined&isOpenedInAgent=undefined&align=Left&ref=" +
                      encodeURIComponent(window.location.protocol + "//" + window.location.host) + '' + '' + '&useJotformSign=Yes' +
                      '&injectCSS=' + encodeURIComponent(window.location.search.indexOf("ndt=1") > -1);
                    _cFieldFrame.addClassName("custom-field-frame-rendered");
                  }
                }
                if (false)
                {
                  var _interval = setInterval(function()
                  {
                    var dataMode = document.querySelector('html').getAttribute('data-mode');
                    if (dataMode === 'fillMode')
                    {
                      renderWidget()
                      clearInterval(_interval);
                    }
                  }, 1000);
                }
                else
                {
                  renderWidget();
                }
              }, 0);
            </script>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_datetime" id="id_88"><label class="form-label form-label-top form-label-auto" id="label_88" for="lite_mode_88" aria-hidden="false"> Date </label>
        <div id="cid_88" class="form-input-wide">
          <div data-wrapper-react="true">
            <div style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input class="currentDate form-textbox validate[limitDate]" id="day_88" name="q88_date[day]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_88 sublabel_88_day" value="22" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="day_88" id="sublabel_88_day" style="min-height:13px">Day</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="month_88" name="q88_date[month]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_88 sublabel_88_month" value="07" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="month_88" id="sublabel_88_month" style="min-height:13px">Month</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="year_88" name="q88_date[year]" type="tel" size="4" data-maxlength="4" data-age="" maxLength="4" autoComplete="off" aria-labelledby="label_88 sublabel_88_year" value="2025" /><label class="form-sub-label" for="year_88" id="sublabel_88_year" style="min-height:13px">Year</label></span></div><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate, validateLiteDate]" id="lite_mode_88" type="text" size="12" data-maxlength="12" maxLength="12" data-age="" data-format="ddmmyyyy" data-seperator="-" placeholder="dd-mm-yyyy" data-placeholder="DD-MM-YYYY" autoComplete="off" aria-labelledby="label_88 sublabel_88_litemode" value="22-07-2025" /><button type="button" class=" newDefaultTheme-dateIcon focusable icon-liteMode" id="input_88_pick" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v1"></button><label class="form-sub-label" for="lite_mode_88" id="sublabel_88_litemode" style="min-height:13px">Date</label></span>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_button" id="id_2">
        <div id="cid_2" class="form-input-wide">
          <div data-align="auto" class="form-buttons-wrapper form-buttons-auto   jsTest-button-wrapperField"><button id="input_2" style="display:none !important" type="button" class="form-submit-button submit-button jf-form-buttons jsTest-submitField " data-component="button" data-content="">Submit</button><button type="button" class="form-submit-button submit-button jf-form-buttons jsTest-submitField useJotformSign-button useJotformSign " data-component="button" data-content="">Submit</button></div>
        </div>
      </li>
      <li style="display:none">Should be Empty: <input type="text" name="website" value="" type="hidden" /></li>
    </ul>
  </div>
  <script>
    JotForm.showJotFormPowered = "new_footer";
  </script>
  <script>
    JotForm.poweredByText = "Powered by Jotform";
  </script><input type="hidden" class="simple_spc" id="simple_spc" name="simple_spc" value="233183619631457" />
  <script type="text/javascript">
    var all_spc = document.querySelectorAll("form[id='233183619631457'] .si" + "mple" + "_spc");
    for (var i = 0; i < all_spc.length; i++)
    {
      all_spc[i].value = "233183619631457-233183619631457";
    }
  </script>
</form></body>
</html><script type="text/javascript">JotForm.isNewSACL=true;</script>