{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9006", "setup-local-ip": "node setup-local-ip.js", "dev:auto-restart": "node auto-restart-dev.js", "build": "next build --debug", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@genkit-ai/googleai": "^1.13.0", "@genkit-ai/next": "^1.13.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@types/better-sqlite3": "^7.6.13", "@types/crypto-js": "^4.2.2", "@types/pdfkit": "^0.17.2", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "csv-parse": "^6.1.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "genkit": "^1.13.0", "lucide-react": "^0.475.0", "next": "15.3.3", "nodemailer": "^6.9.14", "patch-package": "^8.0.0", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-signature-canvas": "^1.0.6", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/nodemailer": "^6.4.15", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-signature-canvas": "^1.0.5", "@types/testing-library__jest-dom": "^5.14.9", "@types/testing-library__react": "^10.0.1", "@types/testing-library__user-event": "^4.1.1", "@types/uuid": "^10.0.0", "cross-env": "^10.0.0", "genkit-cli": "^1.13.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}