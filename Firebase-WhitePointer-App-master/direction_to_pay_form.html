<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en-US"  class="supernova "><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="alternate" type="application/json+oembed" href="https://www.jotform.com/oembed/?format=json&amp;url=https%3A%2F%2Fform.jotform.com%2F***************" title="oEmbed Form">
<link rel="alternate" type="text/xml+oembed" href="https://www.jotform.com/oembed/?format=xml&amp;url=https%3A%2F%2Fform.jotform.com%2F***************" title="oEmbed Form">
<meta property="og:title" content="WP DIRECTION TO PAY TO: AAI Limitedt/as Suncorp Insurances" >
<meta property="og:url" content="https://render.jotform.com/***************" >
<meta property="og:description" content="Please click the link to complete this form." >
<meta name="slack-app-id" content="AHNMASS8M">
<meta property="og:image" content="https://cdn.jotfor.ms/assets/img/landing/opengraph.png" />
<link rel="shortcut icon" href="https://cdn.jotfor.ms/assets/img/favicons/favicon-2021-light%402x.png">
<link rel="apple-touch-icon" href="https://cdn.jotfor.ms/assets/img/favicons/favicon-2021-light%402x.png">
<script>
          var favicon = document.querySelector('link[rel="shortcut icon"]');
          window.isDarkMode = (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches);
          if(favicon && window.isDarkMode) {
              favicon.href = favicon.href.replaceAll('favicon-2021-light%402x.png', 'favicon-2021-dark%402x.png');
          }
      </script><link rel="canonical" href="https://render.jotform.com/***************" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=1" />
<meta name="HandheldFriendly" content="true" />
<title>WP DIRECTION TO PAY TO: AAI Limitedt/as Suncorp Insurances</title>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/stylebuilder/static/form-common.css?v=a9a303c
"/>
<style type="text/css">@media print{*{-webkit-print-color-adjust: exact !important;color-adjust: exact !important;}.form-section{display:inline!important}.form-pagebreak{display:none!important}.form-section-closed{height:auto!important}.page-section{position:initial!important}}</style>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/themes/CSS/5e6b428acc8c4e222d1beb91.css?v=3.3.64282"/>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/css/styles/payment/payment_styles.css?3.3.64282" />
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/css/styles/payment/payment_feature.css?3.3.64282" />
<script>window.enableEventObserver=true</script>
<!--[if lt IE 9]>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/flashcanvas.js" type="text/javascript"></script>
<![endif]-->
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jquery-3.7.1.min.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.CompressorBase30.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.CompressorSVG.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.UndoButton.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jotform.signaturepad.new.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/static/prototype.forms.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/static/jotform.forms.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/maskedinput_5.0.9.min.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/umd/df28ddf41e3/for-sign-form-integration.js" type="text/javascript" defer></script>
<script src="https://cdn.jotfor.ms/s/umd/df28ddf41e3/for-form-branding-footer.js" type="text/javascript" defer></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/smoothscroll.min.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/errorNavigation.js" type="text/javascript"></script>
<script type="text/javascript">	JotForm.newDefaultTheme = true;
	JotForm.extendsNewTheme = false;
	JotForm.CDN_VENDOR_PATH = "https://cdn.jotfor.ms/s/vendor/static";
	JotForm.singleProduct = false;
	JotForm.newPaymentUIForNewCreatedForms = true;
	JotForm.texts = {"confirmEmail":"E-mail does not match","pleaseWait":"Please wait...","validateEmail":"You need to validate this e-mail","confirmClearForm":"Are you sure you want to clear the form","lessThan":"Your score should be less than or equal to","incompleteFields":"There are incomplete required fields. Please complete them.","required":"This field is required.","requireOne":"At least one field required.","requireEveryRow":"Every row is required.","requireEveryCell":"Every cell is required.","email":"Enter a valid e-mail address","alphabetic":"This field can only contain letters","numeric":"This field can only contain numeric values","alphanumeric":"This field can only contain letters and numbers.","cyrillic":"This field can only contain cyrillic characters","url":"This field can only contain a valid URL","currency":"This field can only contain currency values.","fillMask":"Field value must fill mask.","uploadExtensions":"You can only upload following files:","noUploadExtensions":"File has no extension file type (e.g. .txt, .png, .jpeg)","uploadFilesize":"File size cannot be bigger than:","uploadFilesizemin":"File size cannot be smaller than:","gradingScoreError":"Score total should only be less than or equal to","inputCarretErrorA":"Input should not be less than the minimum value:","inputCarretErrorB":"Input should not be greater than the maximum value:","maxDigitsError":"The maximum digits allowed is","minCharactersError":"The number of characters should not be less than the minimum value:","maxCharactersError":"The number of characters should not be more than the maximum value:","freeEmailError":"Free email accounts are not allowed","minSelectionsError":"The minimum required number of selections is ","maxSelectionsError":"The maximum number of selections allowed is ","pastDatesDisallowed":"Date must not be in the past.","dateLimited":"This date is unavailable.","dateInvalid":"This date is not valid. The date format is {format}","dateInvalidSeparate":"This date is not valid. Enter a valid {element}.","ageVerificationError":"You must be older than {minAge} years old to submit this form.","multipleFileUploads_typeError":"{file} has invalid extension. Only {extensions} are allowed.","multipleFileUploads_sizeError":"{file} is too large, maximum file size is {sizeLimit}.","multipleFileUploads_minSizeError":"{file} is too small, minimum file size is {minSizeLimit}.","multipleFileUploads_emptyError":"{file} is empty, please select files again without it.","multipleFileUploads_uploadFailed":"File upload failed, please remove it and upload the file again.","multipleFileUploads_onLeave":"The files are being uploaded, if you leave now the upload will be cancelled.","multipleFileUploads_fileLimitError":"Only {fileLimit} file uploads allowed.","dragAndDropFilesHere_infoMessage":"Drag and drop files here","chooseAFile_infoMessage":"Choose a file","maxFileSize_infoMessage":"Max. file size","generalError":"There are errors on the form. Please fix them before continuing.","generalPageError":"There are errors on this page. Please fix them before continuing.","wordLimitError":"Too many words. The limit is","wordMinLimitError":"Too few words.  The minimum is","characterLimitError":"Too many Characters.  The limit is","characterMinLimitError":"Too few characters. The minimum is","ccInvalidNumber":"Credit Card Number is invalid.","ccInvalidCVC":"CVC number is invalid.","ccInvalidExpireDate":"Expire date is invalid.","ccInvalidExpireMonth":"Expiration month is invalid.","ccInvalidExpireYear":"Expiration year is invalid.","ccMissingDetails":"Please fill up the credit card details.","ccMissingProduct":"Please select at least one product.","ccMissingDonation":"Please enter numeric values for donation amount.","disallowDecimals":"Please enter a whole number.","restrictedDomain":"This domain is not allowed","ccDonationMinLimitError":"Minimum amount is {minAmount} {currency}","requiredLegend":"All fields marked with * are required and must be filled.","geoPermissionTitle":"Permission Denied","geoPermissionDesc":"Check your browser's privacy settings.","geoNotAvailableTitle":"Position Unavailable","geoNotAvailableDesc":"Location provider not available. Please enter the address manually.","geoTimeoutTitle":"Timeout","geoTimeoutDesc":"Please check your internet connection and try again.","selectedTime":"Selected Time","formerSelectedTime":"Former Time","cancelAppointment":"Cancel Appointment","cancelSelection":"Cancel Selection","confirmSelection":"Confirm Selection","noSlotsAvailable":"No slots available","slotUnavailable":"{time} on {date} has been selected is unavailable. Please select another slot.","multipleError":"There are {count} errors on this page. Please correct them before moving on.","oneError":"There is {count} error on this page. Please correct it before moving on.","doneMessage":"Well done! All errors are fixed.","invalidTime":"Enter a valid time","doneButton":"Done","reviewSubmitText":"Review and Submit","nextButtonText":"Next","prevButtonText":"Previous","seeErrorsButton":"See Errors","notEnoughStock":"Not enough stock for the current selection","notEnoughStock_remainedItems":"Not enough stock for the current selection ({count} items left)","soldOut":"Sold Out","justSoldOut":"Just Sold Out","selectionSoldOut":"Selection Sold Out","subProductItemsLeft":"({count} items left)","startButtonText":"START","submitButtonText":"Submit","submissionLimit":"Sorry! Only one entry is allowed. <br> Multiple submissions are disabled for this form.","reviewBackText":"Back to Form","seeAllText":"See All","progressMiddleText":"of","fieldError":"field has an error.","error":"Error"};
	JotForm.newPaymentUI = true;
	JotForm.useJotformSign = "Yes";
	JotForm.isFormViewTrackingAllowed = true;
	JotForm.replaceTagTest = true;
	JotForm.activeRedirect = "thanktext";
	JotForm.uploadServerURL = "https://upload.jotform.com/upload";
	JotForm.clearFieldOnHide="disable";
	JotForm.submitError="jumpToFirstError";
	window.addEventListener('DOMContentLoaded',function(){window.brandingFooter.init({"formID":***************,"campaign":"powered_by_jotform_le","isCardForm":false,"isLegacyForm":true,"formLanguage":"en"})});
	JotForm.init(function(){
	/*INIT-START*/

 JotForm.calendarMonths = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewMonths) JotForm.calenderViewMonths = {};  JotForm.calenderViewMonths[9] = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewDays) JotForm.calenderViewDays = {};  JotForm.calenderViewDays[9] = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarDays = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarOther = {"today":"Today"};
 var languageOptions = document.querySelectorAll('#langList li'); 
 for(var langIndex = 0; langIndex < languageOptions.length; langIndex++) { 
   languageOptions[langIndex].on('click', function(e) { setTimeout(function(){ JotForm.setCalendar("9", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); }, 0); });
 } 
 JotForm.onTranslationsFetch(function() { JotForm.setCalendar("9", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); });
if (window.JotForm && JotForm.accessible) $('input_11').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_14').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_16').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_15').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_17').setAttribute('tabindex',0);

 JotForm.calendarMonths = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewMonths) JotForm.calenderViewMonths = {};  JotForm.calenderViewMonths[19] = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewDays) JotForm.calenderViewDays = {};  JotForm.calenderViewDays[19] = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarDays = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarOther = {"today":"Today"};
 var languageOptions = document.querySelectorAll('#langList li'); 
 for(var langIndex = 0; langIndex < languageOptions.length; langIndex++) { 
   languageOptions[langIndex].on('click', function(e) { setTimeout(function(){ JotForm.setCalendar("19", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); }, 0); });
 } 
 JotForm.onTranslationsFetch(function() { JotForm.setCalendar("19", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); });
 JotForm.formatDate({date:(new Date()), dateField:$("id_"+19)});
if (window.JotForm && JotForm.accessible) $('input_20').setAttribute('tabindex',0);
      JotForm.alterTexts(undefined);
	/*INIT-END*/
	});

   setTimeout(function() {
JotForm.paymentExtrasOnTheFly([null,{"name":"heading","qid":"1","text":"DIRECTION TO PAY TO: AAI Limitedt\u002Fas Suncorp Insurance","type":"control_head"},{"name":"submit2","qid":"2","text":"Continue","type":"control_button"},null,null,null,null,{"description":"","name":"reClaimant","qid":"7","text":"RE: Claimant ","type":"control_fullname"},null,{"description":"","name":"dob","qid":"9","text":"DOB","type":"control_datetime"},{"description":"","name":"address","qid":"10","text":"Address","type":"control_address"},{"description":"","name":"claimantNumber","qid":"11","subLabel":"","text":"Claimant Number","type":"control_textbox"},null,{"description":"","name":"iHereby","qid":"13","subLabel":"Signature","text":"I hereby authorise and direct you to forward monies payable to me to White Pointer Recoveries PtyLtd:","type":"control_signature"},{"description":"","name":"bank","qid":"14","subLabel":"","text":"Bank","type":"control_textbox"},{"description":"","name":"accountNumber","qid":"15","subLabel":"","text":"Account Number","type":"control_textbox"},{"description":"","name":"accountName","qid":"16","subLabel":"","text":"Account Name","type":"control_textbox"},{"description":"","name":"bsb","qid":"17","subLabel":"Use as payment reference:","text":"BSB","type":"control_textbox"},{"description":"","name":"witness","qid":"18","subLabel":"Signature","text":"Witness ","type":"control_signature"},{"description":"","name":"dateSigned","qid":"19","text":"Date Signed","type":"control_datetime"},{"description":"","name":"witnessName","qid":"20","subLabel":"","text":"Witness Name ","type":"control_textbox"}]);}, 20); 
</script>
</head>
<body>
<form class="jotform-form" onsubmit="return typeof testSubmitFunction !== 'undefined' && testSubmitFunction();" action="https://submit.jotform.com/submit/***************" method="post" name="form_***************" id="***************" accept-charset="utf-8" autocomplete="on"><input type="hidden" name="formID" value="***************" /><input type="hidden" id="JWTContainer" value="" /><input type="hidden" id="cardinalOrderNumber" value="" /><input type="hidden" id="jsExecutionTracker" name="jsExecutionTracker" value="build-date-1753767497542" /><input type="hidden" id="submitSource" name="submitSource" value="unknown" /><input type="hidden" id="submitDate" name="submitDate" value="undefined" /><input type="hidden" id="buildDate" name="buildDate" value="1753767497542" /><input type="hidden" name="uploadServerUrl" value="https://upload.jotform.com/upload" /><input type="hidden" name="eventObserver" value="1" />
  <div role="main" class="form-all">
    <ul class="form-section page-section" role="presentation">
      <li id="cid_1" class="form-input-wide" data-type="control_head">
        <div class="form-header-group  header-large">
          <div class="header-text httal htvam">
            <h1 id="header_1" class="form-header" data-component="header">DIRECTION TO PAY TO: AAI Limitedt/as Suncorp Insurance</h1>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_fullname" id="id_7"><label class="form-label form-label-top form-label-auto" id="label_7" for="first_7" aria-hidden="false"> RE: Claimant </label>
        <div id="cid_7" class="form-input-wide" data-layout="full">
          <div data-wrapper-react="true"><span class="form-sub-label-container" style="vertical-align:top" data-input-type="first"><input type="text" id="first_7" name="q7_reClaimant[first]" class="form-textbox" data-defaultvalue="" autoComplete="section-input_7 given-name" size="10" data-component="first" aria-labelledby="label_7 sublabel_7_first" value="" /><label class="form-sub-label" for="first_7" id="sublabel_7_first" style="min-height:13px">First Name</label></span><span class="form-sub-label-container" style="vertical-align:top" data-input-type="last"><input type="text" id="last_7" name="q7_reClaimant[last]" class="form-textbox" data-defaultvalue="" autoComplete="section-input_7 family-name" size="15" data-component="last" aria-labelledby="label_7 sublabel_7_last" value="" /><label class="form-sub-label" for="last_7" id="sublabel_7_last" style="min-height:13px">Last Name</label></span></div>
        </div>
      </li>
      <li class="form-line" data-type="control_datetime" id="id_9"><label class="form-label form-label-top form-label-auto" id="label_9" for="lite_mode_9" aria-hidden="false"> DOB </label>
        <div id="cid_9" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="day_9" name="q9_dob[day]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_9 sublabel_9_day" value="" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="day_9" id="sublabel_9_day" style="min-height:13px">Day</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="month_9" name="q9_dob[month]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_9 sublabel_9_month" value="" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="month_9" id="sublabel_9_month" style="min-height:13px">Month</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="year_9" name="q9_dob[year]" type="tel" size="4" data-maxlength="4" data-age="" maxLength="4" autoComplete="off" aria-labelledby="label_9 sublabel_9_year" value="" /><label class="form-sub-label" for="year_9" id="sublabel_9_year" style="min-height:13px">Year</label></span></div><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate, validateLiteDate]" id="lite_mode_9" type="text" size="12" data-maxlength="12" maxLength="12" data-age="" data-format="ddmmyyyy" data-seperator="-" placeholder="DD-MM-YYYY" data-placeholder="DD-MM-YYYY" autoComplete="off" aria-labelledby="label_9 sublabel_9_litemode" value="" /><button type="button" class=" newDefaultTheme-dateIcon focusable icon-liteMode" id="input_9_pick" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v2"></button><label class="form-sub-label" for="lite_mode_9" id="sublabel_9_litemode" style="min-height:13px">Date</label></span>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_address" id="id_10" data-compound-hint=",,,,Please Select,,Please Select,"><label class="form-label form-label-top form-label-auto" id="label_10" for="input_10_addr_line1" aria-hidden="false"> Address </label>
        <div id="cid_10" class="form-input-wide" data-layout="full">
          <div summary="" class="form-address-table jsTest-addressField">
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField"><span class="form-address-line form-address-street-line jsTest-address-lineField"><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_10_addr_line1" name="q10_address[addr_line1]" class="form-textbox form-address-line" data-defaultvalue="" autoComplete="section-input_10 address-line1" data-component="address_line_1" aria-labelledby="label_10 sublabel_10_addr_line1" value="" /><label class="form-sub-label" for="input_10_addr_line1" id="sublabel_10_addr_line1" style="min-height:13px">Street Address</label></span></span></div>
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField"><span class="form-address-line form-address-street-line jsTest-address-lineField"><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_10_addr_line2" name="q10_address[addr_line2]" class="form-textbox form-address-line" data-defaultvalue="" autoComplete="section-input_10 address-line2" data-component="address_line_2" aria-labelledby="label_10 sublabel_10_addr_line2" value="" /><label class="form-sub-label" for="input_10_addr_line2" id="sublabel_10_addr_line2" style="min-height:13px">Street Address Line 2</label></span></span></div>
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField"><span class="form-address-line form-address-city-line jsTest-address-lineField "><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_10_city" name="q10_address[city]" class="form-textbox form-address-city" data-defaultvalue="" autoComplete="section-input_10 address-level2" data-component="city" aria-labelledby="label_10 sublabel_10_city" value="" /><label class="form-sub-label" for="input_10_city" id="sublabel_10_city" style="min-height:13px">City</label></span></span><span class="form-address-line form-address-state-line jsTest-address-lineField "><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_10_state" name="q10_address[state]" class="form-textbox form-address-state" data-defaultvalue="" autoComplete="section-input_10 address-level1" data-component="state" aria-labelledby="label_10 sublabel_10_state" value="" /><label class="form-sub-label" for="input_10_state" id="sublabel_10_state" style="min-height:13px">State / Province</label></span></span></div>
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField"><span class="form-address-line form-address-zip-line jsTest-address-lineField "><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_10_postal" name="q10_address[postal]" class="form-textbox form-address-postal" data-defaultvalue="" autoComplete="section-input_10 postal-code" data-component="zip" aria-labelledby="label_10 sublabel_10_postal" value="" /><label class="form-sub-label" for="input_10_postal" id="sublabel_10_postal" style="min-height:13px">Postal / Zip Code</label></span></span></div>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_11"><label class="form-label form-label-top form-label-auto" id="label_11" for="input_11" aria-hidden="false"> Claimant Number </label>
        <div id="cid_11" class="form-input-wide" data-layout="half"> <input type="text" id="input_11" name="q11_claimantNumber" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:310px" size="310" data-component="textbox" aria-labelledby="label_11" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_14"><label class="form-label form-label-top form-label-auto" id="label_14" for="input_14" aria-hidden="false"> Bank </label>
        <div id="cid_14" class="form-input-wide" data-layout="half"> <input type="text" id="input_14" name="q14_bank" data-type="input-textbox" class="form-readonly form-textbox" data-defaultvalue="Commonwealth Bank" style="width:310px" size="310" tabindex="-1" data-component="textbox" aria-labelledby="label_14" readonly="" value="Commonwealth Bank" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_16"><label class="form-label form-label-top form-label-auto" id="label_16" for="input_16" aria-hidden="false"> Account Name </label>
        <div id="cid_16" class="form-input-wide" data-layout="half"> <input type="text" id="input_16" name="q16_accountName" data-type="input-textbox" class="form-readonly form-textbox" data-defaultvalue="White Pointer Recoveries PTY LTD" style="width:310px" size="310" tabindex="-1" data-component="textbox" aria-labelledby="label_16" readonly="" value="White Pointer Recoveries PTY LTD" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_15"><label class="form-label form-label-top form-label-auto" id="label_15" for="input_15" aria-hidden="false"> Account Number </label>
        <div id="cid_15" class="form-input-wide" data-layout="half"> <input type="text" id="input_15" name="q15_accountNumber" data-type="input-textbox" class="form-readonly form-textbox" data-defaultvalue="********" style="width:310px" size="310" tabindex="-1" data-component="textbox" aria-labelledby="label_15" readonly="" value="********" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_17"><label class="form-label form-label-top form-label-auto" id="label_17" for="input_17" aria-hidden="false"> BSB </label>
        <div id="cid_17" class="form-input-wide" data-layout="half"> <span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_17" name="q17_bsb" data-type="input-textbox" class="form-readonly form-textbox" data-defaultvalue="062-452" style="width:310px" size="310" tabindex="-1" data-component="textbox" aria-labelledby="label_17 sublabel_input_17" readonly="" value="062-452" /><label class="form-sub-label" for="input_17" id="sublabel_input_17" style="min-height:13px">Use as payment reference:</label></span> </div>
      </li>
      <li class="form-line" data-type="control_signature" id="id_13"><label class="form-label form-label-top form-label-auto" id="label_13" for="input_13" aria-hidden="false"> I hereby authorise and direct you to forward monies payable to me to White Pointer Recoveries PtyLtd: </label>
        <div id="cid_13" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div id="signature_pad_13" class="signature-pad-wrapper">
              <div data-wrapper-react="true">
                <!--[if IE 7]><script type="text/javascript" src="/js/vendor/json2.js"></script><![endif]-->
              </div>
              <div class="signature-line signature-wrapper signature-placeholder" data-component="signature">
                <div id="sig_pad_13" data-width="310" data-height="114" data-id="13" data-required="false" class="pad " aria-description="Use your pointer or touch input to draw your signature." aria-labelledby="label_13" tabindex="0"></div><input type="hidden" name="q13_iHereby" class="output4" id="input_13" />
              </div>
              <aside class="signature-pad-aside"><a style="margin-top:2px;font-size:10px;color:inherit;text-decoration:none" href="https://www.jotform.com/products/sign?utm_source=sign_cardform&amp;utm_content=form&amp;utm_medium=button&amp;utm_campaign=sign_form_integration" target="_blank">Powered by <span style="color:#57810b;font-weight:700">Jotform Sign</span></a><span class="clear-pad-btn clear-pad" role="button" tabindex="0">Clear</span></aside><span class="form-sub-label-container" style="vertical-align:top"><label class="form-sub-label" for="signature_pad_13" style="min-height:13px">Signature</label></span>
            </div>
            <div data-wrapper-react="true">
              <script type="text/javascript">
                window.signatureForm = true
              </script>
            </div>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_signature" id="id_18"><label class="form-label form-label-top form-label-auto" id="label_18" for="input_18" aria-hidden="false"> Witness </label>
        <div id="cid_18" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div id="signature_pad_18" class="signature-pad-wrapper">
              <div data-wrapper-react="true">
                <!--[if IE 7]><script type="text/javascript" src="/js/vendor/json2.js"></script><![endif]-->
              </div>
              <div class="signature-line signature-wrapper signature-placeholder" data-component="signature">
                <div id="sig_pad_18" data-width="310" data-height="114" data-id="18" data-required="false" class="pad " aria-description="Use your pointer or touch input to draw your signature." aria-labelledby="label_18" tabindex="0"></div><input type="hidden" name="q18_witness" class="output4" id="input_18" />
              </div>
              <aside class="signature-pad-aside"><a style="margin-top:2px;font-size:10px;color:inherit;text-decoration:none" href="https://www.jotform.com/products/sign?utm_source=sign_cardform&amp;utm_content=form&amp;utm_medium=button&amp;utm_campaign=sign_form_integration" target="_blank">Powered by <span style="color:#57810b;font-weight:700">Jotform Sign</span></a><span class="clear-pad-btn clear-pad" role="button" tabindex="0">Clear</span></aside><span class="form-sub-label-container" style="vertical-align:top"><label class="form-sub-label" for="signature_pad_18" style="min-height:13px">Signature</label></span>
            </div>
            <div data-wrapper-react="true">
              <script type="text/javascript">
                window.signatureForm = true
              </script>
            </div>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_datetime" id="id_19"><label class="form-label form-label-top form-label-auto" id="label_19" for="lite_mode_19" aria-hidden="false"> Date Signed </label>
        <div id="cid_19" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input class="currentDate form-textbox validate[limitDate]" id="day_19" name="q19_dateSigned[day]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_19 sublabel_19_day" value="29" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="day_19" id="sublabel_19_day" style="min-height:13px">Day</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="month_19" name="q19_dateSigned[month]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_19 sublabel_19_month" value="07" /><span class="date-separate" aria-hidden="true"> -</span><label class="form-sub-label" for="month_19" id="sublabel_19_month" style="min-height:13px">Month</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="year_19" name="q19_dateSigned[year]" type="tel" size="4" data-maxlength="4" data-age="" maxLength="4" autoComplete="off" aria-labelledby="label_19 sublabel_19_year" value="2025" /><label class="form-sub-label" for="year_19" id="sublabel_19_year" style="min-height:13px">Year</label></span></div><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate, validateLiteDate]" id="lite_mode_19" type="text" size="12" data-maxlength="12" maxLength="12" data-age="" data-format="ddmmyyyy" data-seperator="-" placeholder="DD-MM-YYYY" data-placeholder="DD-MM-YYYY" autoComplete="off" aria-labelledby="label_19 sublabel_19_litemode" value="29-07-2025" /><button type="button" class=" newDefaultTheme-dateIcon focusable icon-liteMode" id="input_19_pick" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v2"></button><label class="form-sub-label" for="lite_mode_19" id="sublabel_19_litemode" style="min-height:13px">Date</label></span>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_20"><label class="form-label form-label-top form-label-auto" id="label_20" for="input_20" aria-hidden="false"> Witness Name </label>
        <div id="cid_20" class="form-input-wide" data-layout="half"> <input type="text" id="input_20" name="q20_witnessName" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:310px" size="310" data-component="textbox" aria-labelledby="label_20" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_button" id="id_2">
        <div id="cid_2" class="form-input-wide" data-layout="full">
          <div data-align="auto" class="form-buttons-wrapper form-buttons-auto   jsTest-button-wrapperField"><button id="input_2" style="display:none !important" type="button" class="form-submit-button submit-button jf-form-buttons jsTest-submitField " data-component="button" data-content="">Continue</button><button type="button" class="form-submit-button submit-button jf-form-buttons jsTest-submitField useJotformSign-button useJotformSign " data-component="button" data-content="">Continue</button></div>
        </div>
      </li>
      <li style="display:none">Should be Empty: <input type="text" name="website" value="" type="hidden" /></li>
    </ul>
  </div>
  <script>
    JotForm.showJotFormPowered = "new_footer";
  </script>
  <script>
    JotForm.poweredByText = "Powered by Jotform";
  </script><input type="hidden" class="simple_spc" id="simple_spc" name="simple_spc" value="***************" />
  <script type="text/javascript">
    var all_spc = document.querySelectorAll("form[id='***************'] .si" + "mple" + "_spc");
    for (var i = 0; i < all_spc.length; i++)
    {
      all_spc[i].value = "***************-***************";
    }
  </script>
</form></body>
</html><script type="text/javascript">JotForm.isNewSACL=true;</script>