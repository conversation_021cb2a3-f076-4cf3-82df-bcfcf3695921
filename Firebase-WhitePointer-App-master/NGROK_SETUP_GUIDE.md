# Ngrok Setup Guide for WhitePointer Testing Environment

## Overview
This guide sets up ngrok for testing email signatures and PDF generation from prefilled forms being signed by customers in a local development environment.

## Quick Setup

### Method 1: Manual Setup
1. **Start ngrok:**
   ```bash
   # Navigate to the ngrok directory
   cd Firebase-WhitePointer-App-master/ngrok-bin
   
   # Start ngrok tunnel
   ngrok http 9003
   ```

2. **Copy the ngrok URL:**
   - Look for the "Forwarding" line in the ngrok output
   - Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)

3. **Update environment variable:**
   ```bash
   # Option A: Use the helper script
   node setup-ngrok-env.js https://abc123.ngrok.io
   
   # Option B: Manually edit .env.local
   # Change: NEXT_PUBLIC_BASE_URL=https://abc123.ngrok.io
   ```

4. **Restart development server:**
   ```bash
   npm run dev
   ```

### Method 2: Using NPM Scripts
1. Start ngrok: `npm run ngrok`
2. Copy the ngrok URL from the output
3. Run: `node setup-ngrok-env.js https://your-url.ngrok.io`
4. Restart the development server

### Method 3: Automated Development (Available)
```bash
npm run dev:ngrok
```
This will automatically start both the development server and ngrok tunnel.

## Testing Scenarios

### Email Signature Testing
1. Send test emails with form links
2. Access forms from mobile devices using ngrok URL
3. Verify signature capture and submission
4. Test PDF generation after form completion

### PDF Generation Testing
1. Fill out customer forms through ngrok URL
2. Submit forms and trigger PDF generation
3. Verify PDF contains correct customer data
4. Test document signing workflow

### Cross-Device Testing
1. Access application from different devices
2. Test form responsiveness on mobile/tablet
3. Verify email links work on all devices
4. Test signature pad functionality across browsers

## Troubleshooting

### Common Issues
- **Ngrok URL not working**: Make sure the development server is running on port 9003
- **Environment variable not updating**: Restart the development server after changing `.env.local`
- **Forms not submitting**: Check that the ngrok tunnel is still active
- **PDF generation failing**: Verify the ngrok URL is accessible and not blocked

### Verification Steps
1. Check ngrok tunnel is active: Visit the ngrok URL in browser
2. Verify environment variable: Check `.env.local` has correct URL
3. Test form submission: Submit a test form and check logs
4. Verify PDF generation: Check if PDFs are created correctly

## Important Notes
- Keep the ngrok command window open during testing
- The ngrok URL changes each time you restart ngrok
- For persistent URLs, consider upgrading to a paid ngrok account
- Always restart the development server after changing environment variables

## Security Considerations
- Only use ngrok for development and testing
- Never expose production databases through ngrok
- Be aware that ngrok URLs are publicly accessible
- Monitor ngrok usage and close tunnels when not needed