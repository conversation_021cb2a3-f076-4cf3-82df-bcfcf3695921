const { spawn } = require('child_process');
const ngrok = require('ngrok');
const fs = require('fs');
const path = require('path');

// Port your Next.js app is running on
const PORT = 9003;

async function startServices() {
  try {
    // Start Next.js dev server
    console.log('Starting Next.js development server...');
    const nextProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'inherit',
      shell: true
    });

    // Wait a moment for the dev server to start
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Start ngrok tunnel
    console.log(`Starting ngrok tunnel to http://localhost:${PORT}...`);
    const url = await ngrok.connect({
      addr: PORT,
      region: 'au', // Australian region for better performance
      onStatusChange: status => {
        console.log(`Ngrok status: ${status}`);
      },
    });

    console.log(`🚀 Ngrok tunnel established at: ${url}`);
    console.log(`👉 Your forms will now be accessible through emails using this public URL`);

    // Create or update .env.local file with the ngrok URL
    const envFilePath = path.join(__dirname, '.env.local');
    let envContent = '';
    
    // Try to read existing .env.local file
    try {
      envContent = fs.readFileSync(envFilePath, 'utf8');
    } catch (err) {
      // File doesn't exist, create a new one
      console.log('Creating new .env.local file...');
    }

    // Replace or add NEXT_PUBLIC_BASE_URL
    if (envContent.includes('NEXT_PUBLIC_BASE_URL=')) {
      envContent = envContent.replace(
        /NEXT_PUBLIC_BASE_URL=.*/,
        `NEXT_PUBLIC_BASE_URL=${url}`
      );
    } else {
      envContent += `\nNEXT_PUBLIC_BASE_URL=${url}\n`;
    }

    // Write updated content to .env.local
    fs.writeFileSync(envFilePath, envContent);
    console.log(`✅ Updated .env.local with NEXT_PUBLIC_BASE_URL=${url}`);

    // Handle cleanup on exit
    process.on('SIGINT', async () => {
      console.log('Shutting down...');
      await ngrok.kill();
      nextProcess.kill();
      process.exit();
    });

  } catch (error) {
    console.error('Error starting services:', error);
    await ngrok.kill();
    process.exit(1);
  }
}

startServices();
