#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 Auto-restart development server for testing...');

let devProcess = null;

function startDevServer() {
  console.log('🚀 Starting development server...');
  
  devProcess = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  });

  devProcess.on('close', (code) => {
    if (code !== 0) {
      console.log(`Dev server exited with code ${code}`);
    }
  });
}

function restartDevServer() {
  console.log('🔄 Restarting development server...');
  
  if (devProcess) {
    devProcess.kill('SIGTERM');
    
    setTimeout(() => {
      startDevServer();
    }, 2000);
  } else {
    startDevServer();
  }
}

// Watch .env.local for changes
const envPath = path.join(__dirname, '.env.local');

if (fs.existsSync(envPath)) {
  console.log('👀 Watching .env.local for changes...');
  
  fs.watchFile(envPath, (curr, prev) => {
    console.log('📝 .env.local changed, restarting server...');
    restartDevServer();
  });
}

// Start initial server
startDevServer();

// Handle cleanup
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  if (devProcess) {
    devProcess.kill('SIGTERM');
  }
  process.exit();
});

console.log('📧 Email signature testing with auto-restart enabled!');
console.log('💡 Change .env.local to automatically restart the server');
console.log('🛑 Press Ctrl+C to stop');