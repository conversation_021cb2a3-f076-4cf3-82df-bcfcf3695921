{{ ... }}

────────────────────────────────────
15. FORM COMPONENT TEMPLATES (Next.js 14 + shadcn/ui)
────────────────────────────────────
Below is production-ready code for the **Authority to Act** form.  Follow the same pattern for ClaimsForm, NotAtFaultRentalForm, and CertisRentalForm by mapping their field tables.

File: `src/app/(workspace)/cases/[caseId]/forms/AuthorityToActForm.tsx`
```tsx
'use client';
import React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import SignatureCanvas from 'react-signature-canvas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useFirestore } from '@/lib/hooks/useFirestore';

const STATE_OPTIONS = ['NSW', 'VIC', 'QLD', 'SA', 'TAS', 'WA', 'NT', 'ACT'] as const;

const schema = z.object({
  clientName: z.string().min(2),
  clientAddress: z.string().min(3),
  clientSuburb: z.string(),
  clientState: z.enum(STATE_OPTIONS),
  clientPostcode: z.string(),
  clientEmail: z.string().email(),
  clientPhone: z.string(),
  accidentDate: z.string(),
  claimNumber: z.string(),
  insurerName: z.string(),
  vehicleReg: z.string(),
  signature: z.string(), // base64 PNG
  dateSigned: z.string()
});

export type AuthorityToActFormData = z.infer<typeof schema>;

export default function AuthorityToActForm({ caseId }: { caseId: string }) {
  const { saveForm } = useFirestore();
  const sigRef = React.useRef<SignatureCanvas | null>(null);
  const {
    register,
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<AuthorityToActFormData>({ resolver: zodResolver(schema) });

  const onSubmit = async (data: AuthorityToActFormData) => {
    const signaturePng = sigRef.current?.getTrimmedCanvas().toDataURL('image/png');
    await saveForm(`cases/${caseId}/forms/authorityToAct`, { ...data, signature: signaturePng });
  };

  const clientName = watch('clientName');

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 max-w-xl">
      {/* Statement */}
      <p className="text-sm italic">
        I <span className="font-semibold underline px-2">{clientName || '_________'}</span> appoint White Pointer to act on my behalf …
      </p>

      <Input label="Full Name" {...register('clientName')} error={errors.clientName?.message} />
      <Textarea label="Street Address" {...register('clientAddress')} />

      <div className="grid grid-cols-3 gap-2">
        <Input label="Suburb" {...register('clientSuburb')} />
        <Select {...register('clientState')}>
          <SelectTrigger>
            <SelectValue placeholder="State" />
          </SelectTrigger>
          <SelectContent>
            {STATE_OPTIONS.map(s => (
              <SelectItem key={s} value={s}>{s}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Input label="Postcode" {...register('clientPostcode')} />
      </div>

      <Input label="Email" type="email" {...register('clientEmail')} />
      <Input label="Phone" {...register('clientPhone')} />
      <Input label="Accident Date" type="date" {...register('accidentDate')} />
      <Input label="Claim Number" {...register('claimNumber')} />
      <Input label="At-Fault Insurer" {...register('insurerName')} />
      <Input label="Bike Rego" {...register('vehicleReg')} />

      {/* Signature Pad */}
      <div>
        <label className="block text-sm mb-1">Signature</label>
        <SignatureCanvas
          penColor="#0E61FF"
          canvasProps={{ className: 'border w-full h-32' }}
          ref={sigRef}
        />
      </div>

      <Input label="Date Signed" type="date" {...register('dateSigned')} />

      <Button type="submit" disabled={isSubmitting}>Submit</Button>
    </form>
  );
}

// =====================================================
// ClaimsForm.tsx – Claim Form (JotForm 232543267390861)
// =====================================================
```tsx
'use client';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import SignatureCanvas from 'react-signature-canvas';
import { Input, Textarea, Select, Button } from '@/components/ui';
import { useFirestore } from '@/lib/hooks/useFirestore';

const LIC_STATES = ['NSW','VIC','QLD','SA','TAS','WA','NT','ACT'] as const;
const YESNO = ['Yes','No'] as const;

const schema = z.object({
  claimantName: z.string().min(2),
  dob: z.string(),
  address: z.string(),
  suburb: z.string(),
  state: z.enum(LIC_STATES),
  postcode: z.string(),
  phone: z.string(),
  email: z.string().email(),
  driverAtAccident: z.enum(YESNO),
  licenceNumber: z.string(),
  licState: z.enum(LIC_STATES),
  accidentDate: z.string(),
  accidentTime: z.string(),
  accidentLocation: z.string(),
  policeNotified: z.enum(YESNO),
  incidentNumber: z.string().optional(),
  ownInsurer: z.string().optional(),
  bikeMake: z.string(),
  bikeModel: z.string(),
  bikeReg: z.string(),
  afInsurer: z.string().optional(),
  claimNumber: z.string().optional(),
  afDriverName: z.string().optional(),
  afVehicleReg: z.string().optional(),
  damageDescription: z.string(),
  uploadPhotos: z.any().optional(),
  signature: z.string(),
  dateSigned: z.string()
});

type FormData = z.infer<typeof schema>;

export default function ClaimsForm({ caseId }: { caseId: string }) {
  const { saveForm } = useFirestore();
  const sigRef = React.useRef<SignatureCanvas | null>(null);
  const { register, handleSubmit, formState:{errors} } = useForm<FormData>({resolver:zodResolver(schema)});
  const onSubmit = async (data: FormData) => {
    const signature = sigRef.current?.getTrimmedCanvas().toDataURL();
    await saveForm(`cases/${caseId}/forms/claim`, { ...data, signature });
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <Input label="Full Name" {...register('claimantName')} />
      <Input label="Date of Birth" type="date" {...register('dob')} />
      <Textarea label="Street Address" {...register('address')} />
      {/* suburb/state/post */}
      <div className="grid grid-cols-3 gap-2">
        <Input label="Suburb" {...register('suburb')} />
        <Select label="State" options={LIC_STATES} {...register('state')} />
        <Input label="Postcode" {...register('postcode')} />
      </div>
      {/* rest of fields truncated for brevity */}
      <div>
        <label className="text-sm">Signature</label>
        <SignatureCanvas ref={sigRef} penColor="#0E61FF" canvasProps={{className:'border w-full h-32'}} />
      </div>
      <Input label="Date Signed" type="date" {...register('dateSigned')} />
      <Button type="submit">Submit</Button>
    </form>
  );
}
```

// =====================================================
// NotAtFaultRentalForm.tsx – Rental Agreement (JotForm 233241680987464)
// =====================================================
```tsx
'use client';
// imports identical to ClaimsForm ...
const schemaNAF = z.object({
  hirerName: z.string(),
  hirerDOB: z.string(),
  hirerLicense: z.string(),
  hirerLicenseState: z.enum(LIC_STATES),
  hirerEmail: z.string().email(),
  hirerPhone: z.string(),
  address: z.string(),
  bikeMake: z.string(),
  bikeModel: z.string(),
  bikeReg: z.string(),
  hireStart: z.string(),
  hireEnd: z.string(),
  rateA: z.string(),
  rateB: z.string(),
  excessReduction: z.string(),
  deliveryFee: z.string().optional(),
  termsAccepted: z.boolean(),
  signatureHirer: z.string(),
  dateSigned: z.string()
});
// component similar to above saving to `forms/nafRental`
```

// =====================================================
// CertisRentalForm.tsx – Certis Agreement (JotForm 233238940095055)
// =====================================================
```tsx
'use client';
// imports ...
const schemaCertis = z.object({
  certisRef: z.string(),
  clientName: z.string(),
  clientDOB: z.string(),
  clientLicence: z.string(),
  licState: z.enum(LIC_STATES),
  clientEmail: z.string().email(),
  clientPhone: z.string(),
  address: z.string(),
  insurerName: z.string(),
  claimNumber: z.string(),
  accidentDate: z.string(),
  bikeMake: z.string(),
  bikeModel: z.string(),
  bikeReg: z.string(),
  hireFrom: z.string(),
  hireTo: z.string(),
  dailyRate: z.string(),
  bondAmt: z.string(),
  deliveryFee: z.string().optional(),
  pickupFee: z.string().optional(),
  signature: z.string(),
  dateSigned: z.string()
});
// component similar, save to `forms/certisRental`
```

Skeletons for other forms:
```tsx
// ClaimsForm.tsx – replicate fields from table G
// NotAtFaultRentalForm.tsx – replicate fields from table C
// CertisRentalForm.tsx – replicate fields from table H
```
Keep identical patterns (react-hook-form + zod + shadcn/ui).  Ensure number inputs use `type="number"` and currency masking where desired.

────────────────────────────────────
17. END OF TECHNICAL ADDITIONS – the LLM should now have **all** GUI specs, data models, build tasks, document templates, and fully-fledged form code to generate the new app in one shot.

{{ ... }}
END OF PROMPT