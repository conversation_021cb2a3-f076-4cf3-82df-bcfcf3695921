import { BikeFrontend } from '@/lib/database-schema';

export const importedBikes: BikeFrontend[] = [
  {
    "id": "M1001",
    "make": "Aprilia",
    "model": "RSV4 Factory",
    "registration": "BD20YZA",
    "registrationExpires": "2025-12-10",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1002",
    "make": "Aprilia",
    "model": "Tuono V4",
    "registration": "BD30CDE",
    "registrationExpires": "2025-12-15",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1003",
    "make": "BMW",
    "model": "F850GS",
    "registration": "BD27TUV",
    "registrationExpires": "2025-10-15",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1004",
    "make": "BMW",
    "model": "R1250GS",
    "registration": "BD37XYZ",
    "registrationExpires": "2025-10-30",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1005",
    "make": "BMW",
    "model": "S1000RR",
    "registration": "BD17PQR",
    "registrationExpires": "2025-11-25",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1006",
    "make": "Ducati",
    "model": "899 Panigale",
    "registration": "BD42MNO",
    "registrationExpires": "2025-12-01",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1007",
    "make": "Ducati",
    "model": "Monster 821",
    "registration": "BD26QRS",
    "registrationExpires": "2025-11-10",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1008",
    "make": "Ducati",
    "model": "Panigale V4",
    "registration": "BD16MNO",
    "registrationExpires": "2025-12-20",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1009",
    "make": "Harley-Davidson",
    "model": "Fat Boy",
    "registration": "BD31FGH",
    "registrationExpires": "2025-11-20",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1010",
    "make": "Harley-Davidson",
    "model": "Sportster S",
    "registration": "BD21BCD",
    "registrationExpires": "2025-11-15",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1011",
    "make": "Honda",
    "model": "Africa Twin",
    "registration": "BD32IJK",
    "registrationExpires": "2025-10-10",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1012",
    "make": "Honda",
    "model": "CB1000R",
    "registration": "BD22EFG",
    "registrationExpires": "2025-10-20",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1013",
    "make": "Honda",
    "model": "CBR1000RR",
    "registration": "BD38ABC",
    "registrationExpires": "2025-12-10",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1014",
    "make": "Honda",
    "model": "CBR600RR",
    "registration": "BD12ABC",
    "registrationExpires": "2025-12-15",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1015",
    "make": "KTM",
    "model": "390 Duke",
    "registration": "BD19VWX",
    "registrationExpires": "2025-09-15",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1016",
    "make": "KTM",
    "model": "890 Adventure",
    "registration": "BD29ZAB",
    "registrationExpires": "2025-08-25",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1017",
    "make": "Kawasaki",
    "model": "Ninja ZX-10R",
    "registration": "BD14GHI",
    "registrationExpires": "2025-10-25",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1018",
    "make": "Kawasaki",
    "model": "Versys 1000",
    "registration": "BD34OPQ",
    "registrationExpires": "2025-08-10",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1019",
    "make": "Kawasaki",
    "model": "Z900",
    "registration": "BD24KLM",
    "registrationExpires": "2025-08-30",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1020",
    "make": "Kawasaki",
    "model": "ZX-6R",
    "registration": "BD40GHI",
    "registrationExpires": "2025-10-20",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1021",
    "make": "Suzuki",
    "model": "GSX-R1000",
    "registration": "BD15JKL",
    "registrationExpires": "2025-09-30",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1022",
    "make": "Suzuki",
    "model": "GSX-R750",
    "registration": "BD41JKL",
    "registrationExpires": "2025-09-25",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1023",
    "make": "Suzuki",
    "model": "SV650",
    "registration": "BD25NOP",
    "registrationExpires": "2025-12-05",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1024",
    "make": "Suzuki",
    "model": "V-Strom 650",
    "registration": "BD35RST",
    "registrationExpires": "2025-12-25",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1025",
    "make": "Triumph",
    "model": "Street Triple RS",
    "registration": "BD18STU",
    "registrationExpires": "2025-10-30",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1026",
    "make": "Triumph",
    "model": "Tiger 900",
    "registration": "BD28WXY",
    "registrationExpires": "2025-09-20",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1027",
    "make": "Yamaha",
    "model": "R6",
    "registration": "BD39DEF",
    "registrationExpires": "2025-11-15",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1028",
    "make": "Yamaha",
    "model": "Tenere 700",
    "registration": "BD33LMN",
    "registrationExpires": "2025-09-05",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  },
  {
    "id": "M1029",
    "make": "Yamaha",
    "model": "YZF-R1",
    "registration": "BD13DEF",
    "registrationExpires": "2025-11-20",
    "serviceCenter": "",
    "deliveryStreet": "",
    "deliverySuburb": "",
    "deliveryState": "",
    "deliveryPostcode": "",
    "lastServiceDate": "",
    "serviceNotes": "",
    "status": "available",
    "location": "Main Warehouse",
    "dailyRate": 85,
    "imageUrl": "https://placehold.co/300x200.png",
    "imageHint": "motorcycle sport",
    "assignment": "-",
    "serviceCenterContactId": "",
    "dailyRateA": 85,
    "dailyRateB": 95,
    "assignedCaseId": "",
    "assignmentStartDate": "",
    "assignmentEndDate": ""
  }
];
