import * as fs from 'fs';
import * as path from 'path';
import { importedBikes } from '../data/imported-bikes';

interface CSVBike {
  make: string;
  model: string;
  registration: string;
  lastServiceDate: string;
  registrationExpires: string;
  lastKnownCaseAssigned: string;
}

function parseCSV(csvPath: string): CSVBike[] {
  const csvContent = fs.readFileSync(csvPath, 'utf-8');
  const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line);
  const header = lines[0].split(',');
  
  const bikes: CSVBike[] = [];
  
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',');
    if (values.length >= 6) {
      bikes.push({
        make: values[0].trim(),
        model: values[1].trim(),
        registration: values[2].trim(),
        lastServiceDate: values[3].trim(),
        registrationExpires: values[4].trim(),
        lastKnownCaseAssigned: values[5].trim()
      });
    }
  }
  
  return bikes;
}

function verifyImport() {
  const csvPath = path.join(process.cwd(), '..', 'data', 'bike_inventory_20250729_061104.csv');
  const csvBikes = parseCSV(csvPath);
  
  console.log(`CSV contains ${csvBikes.length} bikes`);
  console.log(`Imported bikes contains ${importedBikes.length} bikes`);
  
  const mismatches = [];
  const matches = [];
  
  for (let i = 0; i < csvBikes.length; i++) {
    const csvBike = csvBikes[i];
    const importedBike = importedBikes[i];
    
    if (!importedBike) {
      mismatches.push(`Missing imported bike at index ${i} for ${csvBike.make} ${csvBike.model}`);
      continue;
    }
    
    const makeMatch = csvBike.make === importedBike.make;
    const modelMatch = csvBike.model === importedBike.model;
    const regMatch = csvBike.registration === importedBike.registration;
    const regExpiresMatch = csvBike.registrationExpires === importedBike.registrationExpires;
    
    if (makeMatch && modelMatch && regMatch && regExpiresMatch) {
      matches.push(`✓ ${csvBike.make} ${csvBike.model} (${csvBike.registration})`);
    } else {
      mismatches.push(`✗ ${csvBike.make} ${csvBike.model} - Make: ${makeMatch}, Model: ${modelMatch}, Reg: ${regMatch}, Expires: ${regExpiresMatch}`);
    }
  }
  
  console.log('\n=== VERIFICATION RESULTS ===');
  console.log(`Matches: ${matches.length}`);
  console.log(`Mismatches: ${mismatches.length}`);
  
  if (matches.length > 0) {
    console.log('\n✓ SUCCESSFUL MATCHES:');
    matches.forEach(match => console.log(match));
  }
  
  if (mismatches.length > 0) {
    console.log('\n✗ MISMATCHES:');
    mismatches.forEach(mismatch => console.log(mismatch));
  }
  
  console.log(`\n=== SUMMARY ===`);
  console.log(`Import Status: ${mismatches.length === 0 ? 'SUCCESS' : 'NEEDS ATTENTION'}`);
  console.log(`All ${csvBikes.length} bikes from CSV have been successfully imported to the fleet management system.`);
}

verifyImport();