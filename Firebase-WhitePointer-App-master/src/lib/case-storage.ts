/**
 * Case storage service using SQLite
 */
import { DatabaseService } from './database';
import type { Case } from '@/lib/firebase-schema-complete';
import { SchemaTransformers } from '@/lib/firebase-schema-complete';

class CaseStorageService {
  async getCases(): Promise<Case[]> {
    try {
      const frontendCases = await DatabaseService.getAllCases();
      return frontendCases.map(SchemaTransformers.caseFrontendToDb);
    } catch (error) {
      console.error('Error fetching cases from database:', error);
      return [];
    }
  }

  async getCase(caseNumber: string): Promise<Case | null> {
    try {
      const frontendCase = await DatabaseService.getCaseByCaseNumber(caseNumber);
      return frontendCase ? SchemaTransformers.caseFrontendToDb(frontendCase) : null;
    } catch (error) {
      console.error('Error fetching case:', error instanceof Error ? error.message : 'Unknown error');
      return null;
    }
  }

  async saveCase(caseData: Partial<Case>): Promise<Case> {
    try {
      if (caseData.id) {
        // This is an update
        await DatabaseService.updateCase(caseData.id, caseData);
        const updatedCase = await DatabaseService.getCaseById(caseData.id);
        if (!updatedCase) throw new Error('Failed to fetch updated case');
        return SchemaTransformers.caseFrontendToDb(updatedCase);
      } else {
        // This is a create
                return await DatabaseService.createCase(caseData as Omit<Case, 'id'>);
      }
    } catch (error) {
      console.error('Error saving case to database:', error);
      throw error;
    }
  }

  async updateCase(caseId: string, updates: Partial<Case>): Promise<Case> {
    try {
      await DatabaseService.updateCase(caseId, updates);
      const updatedCase = await DatabaseService.getCaseById(caseId);
      if (!updatedCase) throw new Error('Failed to fetch updated case');
      return SchemaTransformers.caseFrontendToDb(updatedCase);
    } catch (error) {
      console.error('Error updating case in database:', error);
      throw error;
    }
  }
}

export const caseStorage = new CaseStorageService();
