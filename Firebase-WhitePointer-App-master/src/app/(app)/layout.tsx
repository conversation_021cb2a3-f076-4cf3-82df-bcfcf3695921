import {
  Sidebar<PERSON>rovider,
  Sidebar,
  <PERSON>bar<PERSON>nse<PERSON>,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Nav } from "@/components/nav"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { AuthGuard } from "@/components/auth-guard"
import { UserHeader } from "@/components/user-header"

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard>
      <SidebarProvider>
        <Sidebar>
          <Nav />
        </Sidebar>
        <SidebarInset>
          <UserHeader />
          <ScrollArea className="h-[calc(100vh-4rem)]">
            <main className="flex-1 p-4 md:p-6 lg:p-8">{children}</main>
          </ScrollArea>
        </SidebarInset>
      </SidebarProvider>
    </AuthGuard>
  )
}
