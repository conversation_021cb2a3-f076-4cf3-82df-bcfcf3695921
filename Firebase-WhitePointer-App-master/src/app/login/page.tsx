"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { initializeLocalStorage } from "@/lib/init-data";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useSessionStorage } from "@/hooks/use-session-storage";
import { useAuth } from "@/context/AuthContext";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import type { Contact } from "@/types/contact";
import type { Workspace } from "@/types/workspace";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [currentUserData, setCurrentUserData] = useState<any>(null);
  const router = useRouter();
  const { toast } = useToast();
  const { login } = useAuth();
  
  const [contacts, setContacts] = useLocalStorage<Contact[]>("contacts", []);
  const [workspaces, setWorkspaces] = useLocalStorage<Workspace[]>("workspaces", []);
  const [, setCurrentUser] = useSessionStorage<any>("currentUser", null);
  const [, setActiveWorkspace] = useSessionStorage<any>("activeWorkspace", null);
  const [rememberedLogin, setRememberedLogin] = useLocalStorage<{ email: string; password: string } | null>("rememberedLogin", null);

  // Initialize data on component mount
  useEffect(() => {
    initializeLocalStorage();
    
    // Force re-read from localStorage
    const storedContacts = localStorage.getItem('contacts');
    const storedWorkspaces = localStorage.getItem('workspaces');
    if (storedContacts) {
      setContacts(JSON.parse(storedContacts));
    }
    if (storedWorkspaces) {
      setWorkspaces(JSON.parse(storedWorkspaces));
    }
    
    // Auto-login with remembered credentials for developers
    if (rememberedLogin) {
      setEmail(rememberedLogin.email);
      setPassword(rememberedLogin.password);
      handleAutoLogin(rememberedLogin.email, rememberedLogin.password);
    }
  }, [setContacts, setWorkspaces, rememberedLogin]);
  
  const handleAutoLogin = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });
      
      const result = await response.json();
      
      if (result.success && result.user) {
        if (result.user.role === 'developer' || result.user.role === 'admin') {
          const userData = {
            id: result.user.id,
            email: result.user.email,
            role: result.user.role,
            name: result.user.role === 'developer' ? 'Developer' : 'Administrator'
          };
          login(userData);
          setActiveWorkspace(null);
          router.push("/cases");
        }
      }
    } catch (error) {
      console.error('Auto-login failed:', error);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });
      
      const result = await response.json();
      
      if (result.success && result.user) {
        const user = result.user;
        
        // Check if user needs to change password
        if (user.first_login || user.status === 'pending_password_change') {
          setCurrentUserData(user);
          setShowPasswordChange(true);
          setIsLoading(false);
          return;
        }
        
        // Developer/Admin login
        if (user.role === 'developer' || user.role === 'admin') {
          const userData = {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.role === 'developer' ? 'Developer' : 'Administrator'
          };
          
          login(userData);
          setActiveWorkspace(null);
          
          // Remember login for developers/admins
          setRememberedLogin({ email, password });
          
          toast({
            title: `Welcome ${user.role === 'developer' ? 'Developer' : 'Administrator'}`,
            description: "You have full access to all workspaces and cases."
          });
          router.push("/cases");
          setIsLoading(false);
          return;
        }
        
        // Workspace user login (lawyer/rental company)
        if (user.contact_id) {
          const contact = contacts.find(c => c.id === user.contact_id);
          const assignedWorkspace = workspaces.find(w => w.contactId === user.contact_id);
          
          if (!contact || !assignedWorkspace) {
            toast({
              variant: "destructive",
              title: "Access Denied",
              description: "Your account is not properly configured. Please contact an administrator."
            });
            setIsLoading(false);
            return;
          }
          
          const userData = {
            id: user.id,
            email: user.email,
            role: "workspace_user",
            name: contact.name,
            contactId: contact.id,
            workspaceId: assignedWorkspace.id
          };
          
          login(userData);
          
          setActiveWorkspace({
            id: assignedWorkspace.id,
            name: contact.name,
            type: contact.type
          });
          
          toast({
            title: `Welcome ${contact.name}`,
            description: `You are now viewing your ${contact.type} workspace.`
          });
          
          router.push("/cases");
          setIsLoading(false);
          return;
        }
      }

      // Fallback to old admin login for compatibility
      if (email === "<EMAIL>" || email === "admin") {
        const userData = {
          id: "admin-legacy",
          email: "<EMAIL>",
          role: "admin",
          name: "Administrator"
        };
        
        login(userData);
        setActiveWorkspace(null);
        toast({
          title: "Welcome Administrator",
          description: "You have full access to all workspaces and cases."
        });
        router.push("/cases");
        setIsLoading(false);
        return;
      }

      // Authentication failed
      toast({
        variant: "destructive",
        title: "Login Failed",
        description: result.error || "Invalid email or password."
      });
      setIsLoading(false);
      
    } catch (error) {
      console.error('Login error:', error);
      toast({
        variant: "destructive",
        title: "Login Failed",
        description: "Network error. Please try again."
      });
      setIsLoading(false);
    }
  };
  
  const handlePasswordChange = async () => {
    if (!currentUserData) return;
    
    if (newPassword !== confirmPassword) {
      toast({
        variant: "destructive",
        title: "Password Mismatch",
        description: "Passwords do not match."
      });
      return;
    }
    
    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: currentUserData.id,
          newPassword: newPassword
        }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setShowPasswordChange(false);
        toast({
          title: "Password Changed",
          description: "Your password has been updated successfully."
        });
        
        // Auto-login with new password
        setPassword(newPassword);
        const loginResponse = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password: newPassword }),
        });
        
        const loginResult = await loginResponse.json();
        if (loginResult.success && loginResult.user) {
          const userData = {
            id: loginResult.user.id,
            email: loginResult.user.email,
            role: loginResult.user.role,
            name: loginResult.user.role === 'developer' ? 'Developer' : 'Administrator'
          };
          login(userData);
          setActiveWorkspace(null);
          router.push("/cases");
        }
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.error || "Failed to change password. Please try again."
        });
      }
    } catch (error) {
      console.error('Password change error:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Network error. Please try again."
      });
    }
  };

  return (
    <>
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">PBikeRescue</CardTitle>
            <CardDescription>
              Sign in to access your workspace
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </form>
            
            <div className="mt-6 pt-6 border-t">
              <div className="text-sm text-gray-600 space-y-2">
                <p className="font-medium">Developer Accounts:</p>
                <div className="space-y-1">
                  <p><strong>Dev 1:</strong> <EMAIL></p>
                  <p><strong>Dev 2:</strong> <EMAIL></p>
                  <p><strong>Password:</strong> Tr@ders84</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Password Change Dialog */}
      <Dialog open={showPasswordChange} onOpenChange={setShowPasswordChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Password Required</DialogTitle>
            <DialogDescription>
              You must change your password before continuing. Please create a secure password with at least 10 characters including uppercase, lowercase, numbers, and special characters.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <Input
                id="newPassword"
                type="password"
                placeholder="Enter new password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="Confirm new password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
            <Button onClick={handlePasswordChange} className="w-full">
              Change Password
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
