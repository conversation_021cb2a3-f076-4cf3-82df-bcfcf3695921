
'use client';

import { useRef, useState } from "react";
import { Bike } from "lucide-react";
import SignatureCanvas from "react-signature-canvas";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";


export default function SignAgreementPage() {
    const { toast } = useToast();
    const router = useRouter();
    const sigPadRef = useRef<SignatureCanvas>(null);
    const [signature, setSignature] = useState<string | null>(null);

    // Mock data - in a real app, this would be fetched based on a token in the URL
    const rentalDetails = {
        make: "Yamaha",
        model: "STREET BOB",
        hireDate: "2024-07-28",
        hireTime: "10:00",
        returnDate: "2024-08-04",
        returnTime: "10:00",
        areaOfUse: "Metro Area - Unlimited KMS",
        hirerName: "John Smith",
        phone: "0412345678",
        address: "123 Example St",
        suburb: "Sydney",
        state: "NSW",
        postCode: "2000",
        dob: "1990-01-15",
        licenceNo: "12345678",
        licenceState: "NSW",
        licenceExp: "2028-01-14",
    }

    const clearSignature = () => {
        sigPadRef.current?.clear();
        setSignature(null);
    }

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (sigPadRef.current?.isEmpty()) {
            toast({
                variant: "destructive",
                title: "Signature Required",
                description: "Please provide your signature before submitting.",
            });
            return;
        }

        const signatureDataUrl = sigPadRef.current?.getTrimmedCanvas().toDataURL("image/png");
        setSignature(signatureDataUrl || null);

        console.log("Signature Data URL:", signatureDataUrl);

        toast({
            title: "Agreement Signed",
            description: "Thank you! The rental agreement has been signed successfully.",
        });

        setTimeout(() => router.push('/'), 3000);
    }

    return (
        <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4 sm:p-6 md:p-8">
             <div className="flex items-center gap-2 mb-6">
                <Bike className="h-8 w-8 text-primary" />
                <span className="text-2xl font-semibold">PBikeRescue</span>
            </div>
            <Card className="w-full max-w-4xl">
                <form onSubmit={handleSubmit}>
                    <CardContent className="p-6 space-y-8">
                        {/* Header */}
                        <div className="text-center space-y-1">
                            <p className="font-bold">ABN 145 224 782</p>
                            <p className="font-bold">Not At Fault PTY LTD</p>
                            <h2 className="text-xl font-semibold text-primary">Rental Contract</h2>
                        </div>
                        
                        <Separator />

                        {/* Vehicle and Hire Details */}
                        <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-1">
                                    <Label htmlFor="make">MAKE</Label>
                                    <Input id="make" readOnly defaultValue={rentalDetails.make} />
                                </div>
                                <div className="space-y-1">
                                    <Label htmlFor="model">MODEL</Label>
                                    <Input id="model" readOnly defaultValue={rentalDetails.model} />
                                </div>
                                <div className="space-y-1">
                                    <Label htmlFor="hireDate">HIRE DATE</Label>
                                    <Input id="hireDate" type="date" readOnly defaultValue={rentalDetails.hireDate} />
                                </div>
                                <div className="space-y-1">
                                    <Label htmlFor="hireTime">HIRE TIME</Label>
                                    <Input id="hireTime" type="time" readOnly defaultValue={rentalDetails.hireTime} />
                                </div>
                            </div>
                             <div className="space-y-1">
                                <Label htmlFor="areaOfUse">AREA OF USE</Label>
                                <Input id="areaOfUse" readOnly defaultValue={rentalDetails.areaOfUse} />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                               <div className="space-y-1">
                                    <Label htmlFor="returnDate">Return Date</Label>
                                    <Input id="returnDate" type="date" readOnly defaultValue={rentalDetails.returnDate} />
                                </div>
                                <div className="space-y-1">
                                    <Label htmlFor="returnTime">Time</Label>
                                    <Input id="returnTime" type="time" readOnly defaultValue={rentalDetails.returnTime} />
                                </div>
                            </div>
                        </div>

                        {/* Hirer Information */}
                        <div className="border rounded-lg p-4 space-y-4">
                             <h3 className="text-lg font-semibold">Hirer Information</h3>
                             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-1">
                                    <Label>Hirer Name</Label>
                                    <Input readOnly defaultValue={rentalDetails.hirerName} />
                                </div>
                                 <div className="space-y-1">
                                    <Label>Phone</Label>
                                    <Input readOnly defaultValue={rentalDetails.phone} />
                                </div>
                             </div>
                              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                                 <div className="space-y-1 md:col-span-3">
                                    <Label>Address</Label>
                                    <Input readOnly defaultValue={rentalDetails.address} />
                                </div>
                                <div className="space-y-1 md:col-span-2">
                                    <Label>Suburb</Label>
                                    <Input readOnly defaultValue={rentalDetails.suburb} />
                                </div>
                              </div>
                               <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                 <div className="space-y-1">
                                    <Label>State</Label>
                                    <Input readOnly defaultValue={rentalDetails.state} />
                                </div>
                                <div className="space-y-1">
                                    <Label>Post Code</Label>
                                    <Input readOnly defaultValue={rentalDetails.postCode} />
                                </div>
                                 <div className="space-y-1">
                                    <Label>DOB</Label>
                                    <Input type="date" readOnly defaultValue={rentalDetails.dob} />
                                </div>
                              </div>
                               <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                 <div className="space-y-1">
                                    <Label>Licence No</Label>
                                    <Input readOnly defaultValue={rentalDetails.licenceNo} />
                                </div>
                                <div className="space-y-1">
                                    <Label>State</Label>
                                    <Input readOnly defaultValue={rentalDetails.licenceState} />
                                </div>
                                 <div className="space-y-1">
                                    <Label>Exp Date</Label>
                                    <Input type="date" readOnly defaultValue={rentalDetails.licenceExp} />
                                </div>
                              </div>
                        </div>
                        
                        {/* Charges */}
                        <div className="border rounded-lg p-4 space-y-4">
                            <h3 className="text-lg font-semibold">Charges</h3>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                 <div className="space-y-1"><Label>Helmet</Label><Input readOnly placeholder="$ /day" /></div>
                                 <div className="space-y-1"><Label>Riding Apparel</Label><Input readOnly placeholder="$" /></div>
                                 <div className="space-y-1"><Label>Admin Fee</Label><Input readOnly placeholder="$" /></div>
                                 <div className="space-y-1"><Label>Delivery/Pick up Fee</Label><Input readOnly placeholder="$" /></div>
                                 <div className="space-y-1"><Label>Additional Driver</Label><Input readOnly placeholder="$ /day" /></div>
                                 <div className="space-y-1"><Label>Excess Reduction</Label><Input readOnly placeholder="$ /day" /></div>
                            </div>
                            <Separator/>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-right"><p className="font-semibold">TOTAL inc GST:</p></div>
                                <div className="font-semibold">$0.00</div>
                                <div className="text-right"><p className="font-semibold">GST Amount:</p></div>
                                <div className="font-semibold">$0.00</div>
                            </div>
                        </div>

                        {/* Terms and Signature */}
                        <div className="space-y-4">
                           <div className="space-y-2">
                                <Label>Terms and Conditions</Label>
                                <div className="h-32 overflow-y-auto rounded-md border p-4 text-sm text-muted-foreground space-y-2">
                                    <p>PENALTY Notice will be charge $40 per notice</p>
                                    <p>Fuel to be returned at same level provided</p>
                                    <p>Driver is responsible for tolls and traffic fines</p>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="signature">Digital Signature</Label>
                                 <div className="rounded-md border border-input bg-background">
                                    <SignatureCanvas 
                                        ref={sigPadRef}
                                        penColor="black"
                                        canvasProps={{ className: 'w-full h-[150px]' }}
                                    />
                                 </div>
                                <Button type="button" variant="link" size="sm" onClick={clearSignature} className="p-0 h-auto">
                                    Clear Signature
                                </Button>
                            </div>
                             <div className="space-y-2">
                                <Label htmlFor="fullName">Full Name</Label>
                                <Input id="fullName" placeholder="Type your full name" required defaultValue={rentalDetails.hirerName} />
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox id="terms" required />
                                <Label htmlFor="terms" className="text-sm font-normal">
                                    I have Read and understood and hereby accept the terms and conditions of this agreement
                                </Label>
                            </div>
                        </div>
                    </CardContent>
                    <CardFooter>
                        <Button type="submit" className="w-full">Sign and Submit Agreement</Button>
                    </CardFooter>
                </form>
            </Card>
        </div>
    )
}
