import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService, ensureDatabaseInitialized } from '@/lib/database';

export async function GET() {
  try {
    ensureDatabaseInitialized();
    const contacts = DatabaseService.getAllContacts();
    return NextResponse.json(contacts);
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return NextResponse.json({ error: 'Failed to fetch contacts', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    ensureDatabaseInitialized();
    const contactData = await request.json();
    const newContact = DatabaseService.createContact(contactData);
    return NextResponse.json(newContact, { status: 201 });
  } catch (error) {
    console.error('Error creating contact:', error);
    return NextResponse.json({ error: 'Failed to create contact', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}