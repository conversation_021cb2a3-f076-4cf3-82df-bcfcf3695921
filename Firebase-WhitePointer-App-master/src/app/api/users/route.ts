import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService, ensureDatabaseInitialized } from '@/lib/database';

export async function GET() {
  try {
    ensureDatabaseInitialized();
    const users = await DatabaseService.getAllUserAccounts();
    // Remove password hashes from response for security
    const safeUsers = users.map(({ password_hash, ...user }) => user);
    return NextResponse.json(safeUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json({ error: 'Failed to fetch users', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    ensureDatabaseInitialized();
    const userData = await request.json();
    const newUser = await DatabaseService.createUserAccount(userData);
    // Remove password hash from response
    const { password_hash, ...safeUser } = newUser;
    return NextResponse.json(safeUser, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json({ error: 'Failed to create user', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}