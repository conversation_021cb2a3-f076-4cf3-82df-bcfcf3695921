import { NextRequest, NextResponse } from 'next/server';
import { changePassword, validatePassword } from '@/lib/user-auth';
import { ensureDatabaseInitialized } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Ensure database is initialized
    ensureDatabaseInitialized();
    
    const { userId, newPassword } = await request.json();
    
    if (!userId || !newPassword) {
      return NextResponse.json(
        { success: false, error: 'User ID and new password are required' },
        { status: 400 }
      );
    }
    
    // Validate password
    const validation = validatePassword(newPassword);
    if (!validation.valid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(', ') },
        { status: 400 }
      );
    }
    
    // Change password
    const success = changePassword(userId, newPassword);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to change password' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Password changed successfully'
    });
    
  } catch (error) {
    console.error('Change password API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}