import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser, initializeDeveloperAccounts } from '@/lib/user-auth';
import { ensureDatabaseInitialized } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Ensure database is initialized
    ensureDatabaseInitialized();
    
    // Initialize developer accounts
    await initializeDeveloperAccounts();
    
    const { email, password } = await request.json();
    
    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }
    
    // Authenticate user
    const authResult = await authenticateUser(email, password);
    
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: 401 }
      );
    }
    
    if (!authResult.user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 401 }
      );
    }
    
    const user = authResult.user;
    
    // Return user data without sensitive information
    const userData = {
      id: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
      first_login: user.first_login,
      contact_id: user.contact_id,
      created_at: user.created_at,
      last_login: user.last_login
    };
    
    return NextResponse.json({
      success: true,
      user: userData
    });
    
  } catch (error) {
    console.error('Login API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}