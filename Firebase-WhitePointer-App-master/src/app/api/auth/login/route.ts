import { NextRequest, NextResponse } from 'next/server';

// Simple authentication without any database dependencies
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    // Simple hardcoded authentication for testing
    if (
      (email === '<EMAIL>' && password === 'Tr@ders84') ||
      (email === 'mi<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com' && password === 'Tr@ders84') ||
      (email === '<EMAIL>' && password === 'admin') ||
      (email === 'admin' && password === 'admin')
    ) {
      return NextResponse.json({
        success: true,
        user: {
          id: 'dev-1',
          email: email,
          role: email.includes('admin') ? 'admin' : 'developer',
          status: 'active',
          first_login: false,
          contact_id: email === '<EMAIL>' ? 'contact-david-001' : null,
          created_at: new Date().toISOString(),
          last_login: new Date().toISOString()
        }
      });
    }
    
    return NextResponse.json(
      { success: false, error: 'Invalid email or password' },
      { status: 401 }
    );
    
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, error: 'Login failed' },
      { status: 500 }
    );
  }
}