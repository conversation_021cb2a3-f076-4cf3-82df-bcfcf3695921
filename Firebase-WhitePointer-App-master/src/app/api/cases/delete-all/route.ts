import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';
import fs from 'fs';
import path from 'path';

export async function DELETE(request: NextRequest) {
  try {
    console.log('🗑️ Starting to delete all cases...');

    // Get all cases first to clean up associated files
    const cases = DatabaseService.getAllCases();
    console.log(`Found ${cases.length} cases to delete`);

    // Clean up associated files for each case
    for (const caseItem of cases) {
      try {
        // Delete uploaded documents
        const documentsDir = path.join(process.cwd(), 'public/uploads/documents', caseItem.id);
        if (fs.existsSync(documentsDir)) {
          fs.rmSync(documentsDir, { recursive: true, force: true });
          console.log(`🗑️ Deleted documents for case ${caseItem.id}`);
        }

        // Delete any signature tokens
        DatabaseService.deleteSignatureTokensByCase(caseItem.id);
        
        // Delete any digital signatures
        DatabaseService.deleteDigitalSignaturesByCase(caseItem.id);
        
      } catch (error) {
        console.error(`❌ Error cleaning up case ${caseItem.id}:`, error);
      }
    }

    // Delete all cases from database
    const deletedCount = DatabaseService.deleteAllCases();
    
    console.log(`✅ Successfully deleted ${deletedCount} cases and cleaned up associated files`);

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${deletedCount} cases and cleaned up associated files`,
      deletedCount
    });

  } catch (error) {
    console.error('❌ Error deleting all cases:', error);
    return NextResponse.json(
      { error: 'Failed to delete all cases', details: error.message },
      { status: 500 }
    );
  }
}
