import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService, ensureDatabaseInitialized } from '@/lib/database';

export async function GET() {
  try {
    ensureDatabaseInitialized();
    const cases = DatabaseService.getAllCases();
    return NextResponse.json(cases);
  } catch (error) {
    console.error('Error fetching cases:', error);
    return NextResponse.json({ error: 'Failed to fetch cases', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    ensureDatabaseInitialized();
    const caseData = await request.json();
    const newCase = DatabaseService.createCase(caseData);
    return NextResponse.json(newCase, { status: 201 });
  } catch (error) {
    console.error('Error creating case:', error);
    return NextResponse.json({ error: 'Failed to create case', details: error instanceof Error ? error.message : 'Unknown error' }, { status: 500 });
  }
}