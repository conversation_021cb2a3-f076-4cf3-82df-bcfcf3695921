import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

const mockCases = [
  {
    caseNumber: '2025-001',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    clientPhone: '0412345678',
    accidentDate: '2025-01-15',
    accidentLocation: 'Collins Street, Melbourne CBD',
    accidentDescription: 'Rear-end collision at traffic lights. C<PERSON> was stationary when hit from behind by a delivery van.',
    atFaultParty: 'ABC Delivery Services',
    atFaultInsurer: 'AAMI Insurance',
    vehicleMake: 'Honda',
    vehicleModel: 'CBR600RR',
    vehicleYear: '2023',
    vehicleRego: 'ABC123',
    damageDescription: 'Rear fairing damaged, tail light broken, minor scratches on exhaust',
    estimatedCost: 2500.00,
    status: 'Active',
    priority: 'High',
    assignedTo: '<PERSON>',
    workspace: 'Melbourne'
  },
  {
    caseNumber: '2025-002',
    clientName: '<PERSON>',
    clientEmail: '<EMAIL>',
    clientPhone: '0423456789',
    accidentDate: '2025-01-18',
    accidentLocation: 'Chapel Street, South Yarra',
    accidentDescription: 'Side-swipe accident during lane change. Other driver failed to check blind spot.',
    atFaultParty: 'Michael Brown',
    atFaultInsurer: 'Budget Direct',
    vehicleMake: 'Yamaha',
    vehicleModel: 'MT-07',
    vehicleYear: '2022',
    vehicleRego: 'DEF456',
    damageDescription: 'Left side panel scratched, mirror damaged, handlebar bent',
    estimatedCost: 1800.00,
    status: 'Active',
    priority: 'Medium',
    assignedTo: 'Mike Chen',
    workspace: 'Melbourne'
  },
  {
    caseNumber: '2025-003',
    clientName: 'David Lee',
    clientEmail: '<EMAIL>',
    clientPhone: '0434567890',
    accidentDate: '2025-01-20',
    accidentLocation: 'King Street, Sydney CBD',
    accidentDescription: 'Intersection collision. Other driver ran red light and T-boned client.',
    atFaultParty: 'Jennifer Davis',
    atFaultInsurer: 'Allianz',
    vehicleMake: 'Kawasaki',
    vehicleModel: 'Ninja 650',
    vehicleYear: '2024',
    vehicleRego: 'GHI789',
    damageDescription: 'Significant damage to right side, engine case cracked, front wheel damaged',
    estimatedCost: 8500.00,
    status: 'Under Review',
    priority: 'High',
    assignedTo: 'Lisa Wang',
    workspace: 'Sydney'
  },
  {
    caseNumber: '2025-004',
    clientName: 'Sophie Taylor',
    clientEmail: '<EMAIL>',
    clientPhone: '**********',
    accidentDate: '2025-01-22',
    accidentLocation: 'Queen Street, Brisbane',
    accidentDescription: 'Parking lot incident. Client was backing out when another vehicle reversed into them.',
    atFaultParty: 'Robert Johnson',
    atFaultInsurer: 'Suncorp',
    vehicleMake: 'Suzuki',
    vehicleModel: 'GSX-R750',
    vehicleYear: '2021',
    vehicleRego: 'JKL012',
    damageDescription: 'Rear section damage, license plate bent, minor scratches',
    estimatedCost: 1200.00,
    status: 'Completed',
    priority: 'Low',
    assignedTo: 'Tom Anderson',
    workspace: 'Brisbane'
  },
  {
    caseNumber: '2025-005',
    clientName: 'Alex Rodriguez',
    clientEmail: '<EMAIL>',
    clientPhone: '**********',
    accidentDate: '2025-01-25',
    accidentLocation: 'Rundle Mall, Adelaide',
    accidentDescription: 'Hit and run incident. Client was parked legally when unknown vehicle struck and fled.',
    atFaultParty: 'Unknown (Hit and Run)',
    atFaultInsurer: 'Under Investigation',
    vehicleMake: 'Ducati',
    vehicleModel: 'Monster 821',
    vehicleYear: '2023',
    vehicleRego: 'MNO345',
    damageDescription: 'Left side damage, mirror missing, tank dented, footpeg bent',
    estimatedCost: 4200.00,
    status: 'Investigation',
    priority: 'Medium',
    assignedTo: 'Rachel Green',
    workspace: 'Adelaide'
  }
];

export async function POST(request: NextRequest) {
  try {
    console.log('🏗️ Creating mock cases...');
    console.log('📊 Mock cases data:', mockCases.length, 'cases to create');

    const createdCases = [];

    for (const mockCase of mockCases) {
      try {
        const caseData = {
          case_number: mockCase.caseNumber,
          client_name: mockCase.clientName,
          client_email: mockCase.clientEmail,
          client_phone: mockCase.clientPhone,
          accident_date: mockCase.accidentDate,
          accident_location: mockCase.accidentLocation,
          accident_description: mockCase.accidentDescription,
          at_fault_party: mockCase.atFaultParty,
          at_fault_insurer: mockCase.atFaultInsurer,
          vehicle_make: mockCase.vehicleMake,
          vehicle_model: mockCase.vehicleModel,
          vehicle_year: mockCase.vehicleYear,
          vehicle_rego: mockCase.vehicleRego,
          damage_description: mockCase.damageDescription,
          estimated_cost: mockCase.estimatedCost,
          status: mockCase.status,
          priority: mockCase.priority,
          assigned_to: mockCase.assignedTo,
          workspace: mockCase.workspace,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const createdCase = DatabaseService.createCase(caseData);
        createdCases.push(createdCase);
        console.log(`✅ Created case: ${mockCase.caseNumber}`);

      } catch (error) {
        console.error(`❌ Error creating case ${mockCase.caseNumber}:`, error);
      }
    }

    console.log(`✅ Successfully created ${createdCases.length} mock cases`);

    return NextResponse.json({
      success: true,
      message: `Successfully created ${createdCases.length} mock cases`,
      cases: createdCases,
      createdCount: createdCases.length
    });

  } catch (error) {
    console.error('❌ Error creating mock cases:', error);
    return NextResponse.json(
      { error: 'Failed to create mock cases', details: error.message },
      { status: 500 }
    );
  }
}
