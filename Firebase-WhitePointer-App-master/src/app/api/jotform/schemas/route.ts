import { NextRequest, NextResponse } from 'next/server';
import { fetchAllJotFormSchemas, fetchJotFormData } from '@/lib/jotform-api';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Fetching JotForm schemas for all document types...');

    // Fetch all form schemas using the API utility
    const schemas = await fetchAllJotFormSchemas();
    
    console.log('✅ Successfully fetched schemas for documents:', Object.keys(schemas));

    return NextResponse.json({
      success: true,
      data: schemas,
      timestamp: new Date().toISOString(),
      documentTypes: Object.keys(schemas)
    });

  } catch (error) {
    console.error('❌ Error fetching JotForm schemas:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Optional: Add POST method for testing specific form IDs
export async function POST(request: NextRequest) {
  try {
    const { formIds } = await request.json();
    
    if (!formIds || !Array.isArray(formIds)) {
      return NextResponse.json({
        success: false,
        error: 'formIds array is required'
      }, { status: 400 });
    }

    console.log('🔄 Fetching JotForm schemas for specific forms:', formIds);
    
    const results: { [key: string]: any } = {};
    
    for (const formId of formIds) {
      try {
        const data = await fetchJotFormData(formId);
        results[formId] = data;
      } catch (error) {
        console.error(`Failed to fetch form ${formId}:`, error);
        results[formId] = { error: error instanceof Error ? error.message : 'Unknown error' };
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching specific JotForm schemas:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}