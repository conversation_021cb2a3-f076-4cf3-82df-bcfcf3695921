"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSessionStorage } from "@/hooks/use-session-storage";

export default function RootPage() {
  const router = useRouter();
  const [currentUser] = useSessionStorage<any>("currentUser", null);

  useEffect(() => {
    if (currentUser) {
      // User is logged in, redirect to dashboard
      router.push("/cases");
    } else {
      // User is not logged in, redirect to login
      router.push("/login");
    }
  }, [currentUser, router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading...</p>
      </div>
    </div>
  );
}
