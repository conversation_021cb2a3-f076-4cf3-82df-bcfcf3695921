
export interface Bike {
  id: string; // Document ID
  make: string;
  model: string;
  registration?: string;
  registrationExpires: string;
  serviceCenter?: string;
  serviceCenterContactId?: string;
  deliveryStreet?: string;
  deliverySuburb?: string;
  deliveryState?: string;
  deliveryPostcode?: string;
  lastServiceDate: string;
  serviceNotes?: string;
  status: "Available" | "Rented" | "Maintenance";
  location: string;
  dailyRate?: number;
  dailyRateA?: number;
  dailyRateB?: number;
  imageUrl: string;
  imageHint: string;
  assignment: string;
  assignedCaseId?: string;
  assignmentStartDate?: string;
  assignmentEndDate?: string;
}
