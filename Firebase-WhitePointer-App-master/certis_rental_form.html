<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en-US"  class="supernova "><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="alternate" type="application/json+oembed" href="https://www.jotform.com/oembed/?format=json&amp;url=https%3A%2F%2Fform.jotform.com%2F233238940095055" title="oEmbed Form">
<link rel="alternate" type="text/xml+oembed" href="https://www.jotform.com/oembed/?format=xml&amp;url=https%3A%2F%2Fform.jotform.com%2F233238940095055" title="oEmbed Form">
<meta property="og:title" content="CERTIS RENTALS PTY LTD" >
<meta property="og:url" content="https://render.jotform.com/233238940095055" >
<meta property="og:description" content="Please click the link to complete this form." >
<meta name="slack-app-id" content="AHNMASS8M">
<meta property="og:image" content="https://cdn.jotfor.ms/assets/img/landing/opengraph.png" />
<link rel="shortcut icon" href="https://cdn.jotfor.ms/assets/img/favicons/favicon-2021-light%402x.png">
<link rel="apple-touch-icon" href="https://cdn.jotfor.ms/assets/img/favicons/favicon-2021-light%402x.png">
<script>
          var favicon = document.querySelector('link[rel="shortcut icon"]');
          window.isDarkMode = (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches);
          if(favicon && window.isDarkMode) {
              favicon.href = favicon.href.replaceAll('favicon-2021-light%402x.png', 'favicon-2021-dark%402x.png');
          }
      </script><link rel="canonical" href="https://render.jotform.com/233238940095055" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=1" />
<meta name="HandheldFriendly" content="true" />
<title>CERTIS RENTALS PTY LTD</title>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/stylebuilder/static/form-common.css?v=a9a303c
"/>
<style type="text/css">@media print{*{-webkit-print-color-adjust: exact !important;color-adjust: exact !important;}.form-section{display:inline!important}.form-pagebreak{display:none!important}.form-section-closed{height:auto!important}.page-section{position:initial!important}}</style>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/themes/CSS/5e6b428acc8c4e222d1beb91.css?v=3.3.64282"/>
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/css/styles/payment/payment_styles.css?3.3.64282" />
<link type="text/css" rel="stylesheet" href="https://cdn.jotfor.ms/css/styles/payment/payment_feature.css?3.3.64282" />
<script>window.enableEventObserver=true</script>
<!--[if lt IE 9]>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/flashcanvas.js" type="text/javascript"></script>
<![endif]-->
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jquery-3.7.1.min.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.CompressorBase30.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.CompressorSVG.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jSignature/jSignature.UndoButton.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/jotform.signaturepad.new.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/static/prototype.forms.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/static/jotform.forms.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/maskedinput_5.0.9.min.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/umd/df28ddf41e3/for-pdfimporter-patch.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/umd/df28ddf41e3/for-sign-form-integration.js" type="text/javascript" defer></script>
<script src="https://cdn.jotfor.ms/s/umd/df28ddf41e3/for-form-branding-footer.js" type="text/javascript" defer></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/vendor/smoothscroll.min.js" type="text/javascript"></script>
<script src="https://cdn.jotfor.ms/s/static/85806824f83/js/errorNavigation.js" type="text/javascript"></script>
<script type="text/javascript">	JotForm.newDefaultTheme = true;
	JotForm.extendsNewTheme = false;
	JotForm.CDN_VENDOR_PATH = "https://cdn.jotfor.ms/s/vendor/static";
	JotForm.singleProduct = false;
	JotForm.newPaymentUIForNewCreatedForms = true;
	JotForm.texts = {"confirmEmail":"E-mail does not match","pleaseWait":"Please wait...","validateEmail":"You need to validate this e-mail","confirmClearForm":"Are you sure you want to clear the form","lessThan":"Your score should be less than or equal to","incompleteFields":"There are incomplete required fields. Please complete them.","required":"This field is required.","requireOne":"At least one field required.","requireEveryRow":"Every row is required.","requireEveryCell":"Every cell is required.","email":"Enter a valid e-mail address","alphabetic":"This field can only contain letters","numeric":"This field can only contain numeric values","alphanumeric":"This field can only contain letters and numbers.","cyrillic":"This field can only contain cyrillic characters","url":"This field can only contain a valid URL","currency":"This field can only contain currency values.","fillMask":"Field value must fill mask.","uploadExtensions":"You can only upload following files:","noUploadExtensions":"File has no extension file type (e.g. .txt, .png, .jpeg)","uploadFilesize":"File size cannot be bigger than:","uploadFilesizemin":"File size cannot be smaller than:","gradingScoreError":"Score total should only be less than or equal to","inputCarretErrorA":"Input should not be less than the minimum value:","inputCarretErrorB":"Input should not be greater than the maximum value:","maxDigitsError":"The maximum digits allowed is","minCharactersError":"The number of characters should not be less than the minimum value:","maxCharactersError":"The number of characters should not be more than the maximum value:","freeEmailError":"Free email accounts are not allowed","minSelectionsError":"The minimum required number of selections is ","maxSelectionsError":"The maximum number of selections allowed is ","pastDatesDisallowed":"Date must not be in the past.","dateLimited":"This date is unavailable.","dateInvalid":"This date is not valid. The date format is {format}","dateInvalidSeparate":"This date is not valid. Enter a valid {element}.","ageVerificationError":"You must be older than {minAge} years old to submit this form.","multipleFileUploads_typeError":"{file} has invalid extension. Only {extensions} are allowed.","multipleFileUploads_sizeError":"{file} is too large, maximum file size is {sizeLimit}.","multipleFileUploads_minSizeError":"{file} is too small, minimum file size is {minSizeLimit}.","multipleFileUploads_emptyError":"{file} is empty, please select files again without it.","multipleFileUploads_uploadFailed":"File upload failed, please remove it and upload the file again.","multipleFileUploads_onLeave":"The files are being uploaded, if you leave now the upload will be cancelled.","multipleFileUploads_fileLimitError":"Only {fileLimit} file uploads allowed.","dragAndDropFilesHere_infoMessage":"Drag and drop files here","chooseAFile_infoMessage":"Choose a file","maxFileSize_infoMessage":"Max. file size","generalError":"There are errors on the form. Please fix them before continuing.","generalPageError":"There are errors on this page. Please fix them before continuing.","wordLimitError":"Too many words. The limit is","wordMinLimitError":"Too few words.  The minimum is","characterLimitError":"Too many Characters.  The limit is","characterMinLimitError":"Too few characters. The minimum is","ccInvalidNumber":"Credit Card Number is invalid.","ccInvalidCVC":"CVC number is invalid.","ccInvalidExpireDate":"Expire date is invalid.","ccInvalidExpireMonth":"Expiration month is invalid.","ccInvalidExpireYear":"Expiration year is invalid.","ccMissingDetails":"Please fill up the credit card details.","ccMissingProduct":"Please select at least one product.","ccMissingDonation":"Please enter numeric values for donation amount.","disallowDecimals":"Please enter a whole number.","restrictedDomain":"This domain is not allowed","ccDonationMinLimitError":"Minimum amount is {minAmount} {currency}","requiredLegend":"All fields marked with * are required and must be filled.","geoPermissionTitle":"Permission Denied","geoPermissionDesc":"Check your browser's privacy settings.","geoNotAvailableTitle":"Position Unavailable","geoNotAvailableDesc":"Location provider not available. Please enter the address manually.","geoTimeoutTitle":"Timeout","geoTimeoutDesc":"Please check your internet connection and try again.","selectedTime":"Selected Time","formerSelectedTime":"Former Time","cancelAppointment":"Cancel Appointment","cancelSelection":"Cancel Selection","confirmSelection":"Confirm Selection","noSlotsAvailable":"No slots available","slotUnavailable":"{time} on {date} has been selected is unavailable. Please select another slot.","multipleError":"There are {count} errors on this page. Please correct them before moving on.","oneError":"There is {count} error on this page. Please correct it before moving on.","doneMessage":"Well done! All errors are fixed.","invalidTime":"Enter a valid time","doneButton":"Done","reviewSubmitText":"Review and Submit","nextButtonText":"Next","prevButtonText":"Previous","seeErrorsButton":"See Errors","notEnoughStock":"Not enough stock for the current selection","notEnoughStock_remainedItems":"Not enough stock for the current selection ({count} items left)","soldOut":"Sold Out","justSoldOut":"Just Sold Out","selectionSoldOut":"Selection Sold Out","subProductItemsLeft":"({count} items left)","startButtonText":"START","submitButtonText":"Submit","submissionLimit":"Sorry! Only one entry is allowed. <br> Multiple submissions are disabled for this form.","reviewBackText":"Back to Form","seeAllText":"See All","progressMiddleText":"of","fieldError":"field has an error.","error":"Error"};
	JotForm.newPaymentUI = true;
	JotForm.importedPDF = "aHR0cHMlM0ElMkYlMkZ3d3cuam90Zm9ybS5jb20lMkZ1cGxvYWRzJTJGd2hpdGVwb2ludGVyMjAxNiUyRmZvcm1fZmlsZXMlMkZwZmNfZmxfNjU1YzIxZTEzZTQzN19DRVJUSVNfUkVOVEFMUy5wZGYlM0ZuYyUzRDE=";
	JotForm.importedPDFSettings = {"isConnected":"Yes","enableThumbnail":"Yes","hasPreviewButton":"No","startButtonText":"Start Filling","formType":"legacyForm","welcomeThumbnail":""};
	JotForm.useJotformSign = "Yes";
	JotForm.isFormViewTrackingAllowed = true;
	JotForm.replaceTagTest = true;
	JotForm.activeRedirect = "thanktext";
	JotForm.uploadServerURL = "https://upload.jotform.com/upload";
	JotForm.clearFieldOnHide="disable";
	JotForm.submitError="jumpToFirstError";
	window.addEventListener('DOMContentLoaded',function(){window.brandingFooter.init({"formID":233238940095055,"campaign":"powered_by_jotform_le","isCardForm":false,"isLegacyForm":true,"formLanguage":"en"})});
	JotForm.init(function(){
	/*INIT-START*/
if (window.JotForm && JotForm.accessible) $('input_4').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_64').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_65').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_66').setAttribute('tabindex',0);

 JotForm.calendarMonths = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewMonths) JotForm.calenderViewMonths = {};  JotForm.calenderViewMonths[8] = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewDays) JotForm.calenderViewDays = {};  JotForm.calenderViewDays[8] = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarDays = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarOther = {"today":"Today"};
 var languageOptions = document.querySelectorAll('#langList li'); 
 for(var langIndex = 0; langIndex < languageOptions.length; langIndex++) { 
   languageOptions[langIndex].on('click', function(e) { setTimeout(function(){ JotForm.setCalendar("8", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); }, 0); });
 } 
 JotForm.onTranslationsFetch(function() { JotForm.setCalendar("8", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); });
if (window.JotForm && JotForm.accessible) $('input_73').setAttribute('tabindex',0);

 JotForm.calendarMonths = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewMonths) JotForm.calenderViewMonths = {};  JotForm.calenderViewMonths[11] = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewDays) JotForm.calenderViewDays = {};  JotForm.calenderViewDays[11] = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarDays = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarOther = {"today":"Today"};
 var languageOptions = document.querySelectorAll('#langList li'); 
 for(var langIndex = 0; langIndex < languageOptions.length; langIndex++) { 
   languageOptions[langIndex].on('click', function(e) { setTimeout(function(){ JotForm.setCalendar("11", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); }, 0); });
 } 
 JotForm.onTranslationsFetch(function() { JotForm.setCalendar("11", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); });
if (window.JotForm && JotForm.accessible) $('input_12').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_13').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_14').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_15').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_16').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_17').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_18').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_19').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_20').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_21').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_22').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_23').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_24').setAttribute('tabindex',0);
      JotForm.setPhoneMaskingValidator( 'input_25_full', '\u0028\u0023\u0023\u0023\u0029 \u0023\u0023\u0023\u002d\u0023\u0023\u0023\u0023' );
if (window.JotForm && JotForm.accessible) $('input_26').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_27').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_28').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_29').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_30').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_31').setAttribute('tabindex',0);

 JotForm.calendarMonths = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewMonths) JotForm.calenderViewMonths = {};  JotForm.calenderViewMonths[32] = ["January","February","March","April","May","June","July","August","September","October","November","December"];
 if (!JotForm.calenderViewDays) JotForm.calenderViewDays = {};  JotForm.calenderViewDays[32] = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarDays = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];
 JotForm.calendarOther = {"today":"Today"};
 var languageOptions = document.querySelectorAll('#langList li'); 
 for(var langIndex = 0; langIndex < languageOptions.length; langIndex++) { 
   languageOptions[langIndex].on('click', function(e) { setTimeout(function(){ JotForm.setCalendar("32", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); }, 0); });
 } 
 JotForm.onTranslationsFetch(function() { JotForm.setCalendar("32", false, {"days":{"monday":true,"tuesday":true,"wednesday":true,"thursday":true,"friday":true,"saturday":true,"sunday":true},"future":true,"past":true,"custom":false,"ranges":false,"start":"","end":"","countSelectedDaysOnly":false}); });
if (window.JotForm && JotForm.accessible) $('input_33').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_34').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_36').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_37').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_38').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_39').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_40').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_41').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_43').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_45').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_70').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_49').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_50').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_51').setAttribute('tabindex',0);
      JotForm.setPhoneMaskingValidator( 'input_53_full', '\u0028\u0023\u0023\u0023\u0029 \u0023\u0023\u0023\u002d\u0023\u0023\u0023\u0023' );
if (window.JotForm && JotForm.accessible) $('input_54').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_55').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_56').setAttribute('tabindex',0);
if (window.JotForm && JotForm.accessible) $('input_57').setAttribute('tabindex',0);
      JotForm.alterTexts(undefined);
	/*INIT-END*/
	});

   setTimeout(function() {
JotForm.paymentExtrasOnTheFly([null,{"name":"certisRentals","qid":"1","text":"CERTIS RENTALS PTY LTD","type":"control_head"},{"name":"abn25","qid":"2","text":"ABN **************","type":"control_head"},{"name":"telNo","qid":"3","text":"Tel No. 02 7906 842 Address: 604-606 Parramatta Road Croydon","type":"control_head"},{"description":"","name":"rentalContract","qid":"4","subLabel":"","text":"Rental Contract","type":"control_textbox"},null,null,null,{"description":"","name":"hireDate","qid":"8","text":"HIRE DATE","type":"control_datetime"},null,{"name":"areaOf","qid":"10","text":"AREA OF USE - Metro Area - Unlimited KMS","type":"control_head"},{"description":"","name":"returnDate","qid":"11","text":"Return Date","type":"control_datetime"},{"description":"","name":"time","qid":"12","subLabel":"","text":"Time","type":"control_textbox"},{"description":"","name":"hirer1","qid":"13","subLabel":"","text":"Hirer 1 Name","type":"control_textbox"},{"description":"","name":"fuelOut","qid":"14","subLabel":"","text":"Fuel Out","type":"control_textbox"},{"description":"","name":"fuelIn","qid":"15","subLabel":"","text":"Fuel In","type":"control_textbox"},{"description":"","name":"address","qid":"16","subLabel":"","text":"Address","type":"control_textbox"},{"description":"","name":"e14","qid":"17","subLabel":"","text":"E   1\u002F4   1\u002F2    3\u002F4    F","type":"control_textbox"},{"description":"","name":"e1418","qid":"18","subLabel":"","text":"E   1\u002F4   1\u002F2    3\u002F4    F","type":"control_textbox"},{"description":"","name":"suburb","qid":"19","subLabel":"","text":"Suburb","type":"control_textbox"},{"description":"","name":"charges","qid":"20","subLabel":"","text":"Charges","type":"control_textbox"},{"description":"","name":"state","qid":"21","subLabel":"","text":"State","type":"control_textbox"},{"description":"","name":"postCode","qid":"22","subLabel":"","text":"Post Code","type":"control_textbox"},{"description":"","name":"day","qid":"23","subLabel":"","text":"@$          \u002Fday","type":"control_textbox"},{"description":"","name":"day24","qid":"24","subLabel":"","text":"@$          \u002Fday","type":"control_textbox"},{"description":"","name":"phone","qid":"25","text":"Phone","type":"control_phone"},{"description":"","name":"excessReduction","qid":"26","subLabel":"","text":"Excess Reduction     @$          \u002Fday","type":"control_textbox"},{"description":"","name":"excessReduction27","qid":"27","subLabel":"","text":"Excess Reduction     @$          \u002Fday","type":"control_textbox"},{"description":"","name":"licenceNo","qid":"28","subLabel":"","text":"Licence No","type":"control_textbox"},{"description":"","name":"state29","qid":"29","subLabel":"","text":"State","type":"control_textbox"},{"description":"","name":"fee","qid":"30","subLabel":"","text":"Fee @$ 6.05\u002Fday","type":"control_textbox"},{"description":"","name":"dob","qid":"31","subLabel":"","text":"DOB","type":"control_textbox"},{"description":"","name":"expDate","qid":"32","text":"Exp Date","type":"control_datetime"},{"description":"","name":"deliverypickUp","qid":"33","subLabel":"","text":"Delivery\u002FPick up Fee @$33","type":"control_textbox"},{"description":"","name":"deliverypickUp34","qid":"34","subLabel":"","text":"Delivery\u002FPick up Fee @$33","type":"control_textbox"},{"description":"","name":"additionalDriver","qid":"35","subLabel":"","text":"ADDITIONAL DRIVER:     Yes\u002F No","type":"control_dropdown"},{"description":"","name":"additionalDriver36","qid":"36","subLabel":"","text":"ADDITIONAL DRIVER:     Yes\u002F No","type":"control_textbox"},{"description":"","name":"helmet","qid":"37","subLabel":"","text":"Helmet","type":"control_textbox"},{"description":"","name":"day38","qid":"38","subLabel":"","text":"\u002Fday","type":"control_textbox"},{"description":"","name":"ridingApparel","qid":"39","subLabel":"","text":"Riding Apparel","type":"control_textbox"},{"description":"","name":"additionalDriver40","qid":"40","subLabel":"","text":"Additional Driver Name","type":"control_textbox"},{"description":"","name":"adminFee","qid":"41","subLabel":"","text":"Admin Fee         @        4.8","type":"control_textbox"},{"name":"divider","qid":"42","text":"Divider","type":"control_divider"},{"description":"","name":"adminFee43","qid":"43","subLabel":"","text":"Admin Fee         @        4.8","type":"control_textbox"},{"name":"divider44","qid":"44","text":"Divider","type":"control_divider"},{"description":"","name":"adminFee45","qid":"45","subLabel":"","text":"Admin Fee         @        4.8","type":"control_textbox"},{"name":"divider46","qid":"46","text":"Divider","type":"control_divider"},{"description":"","name":"address47","qid":"47","text":"Address","type":"control_address"},null,{"description":"","name":"suburb49","qid":"49","subLabel":"","text":"Suburb","type":"control_textbox"},{"description":"","name":"totalInc","qid":"50","subLabel":"","text":"TOTAL inc GST","type":"control_textbox"},{"description":"","name":"gstAmount","qid":"51","subLabel":"","text":"GST Amount","type":"control_textbox"},null,{"description":"","name":"phone53","qid":"53","text":"Phone","type":"control_phone"},{"description":"","name":"licenceNo54","qid":"54","subLabel":"","text":"Licence No.","type":"control_textbox"},{"description":"","name":"state55","qid":"55","subLabel":"","text":"State","type":"control_textbox"},{"description":"","name":"dob56","qid":"56","subLabel":"","text":"DOB","type":"control_textbox"},{"description":"","name":"expiry","qid":"57","subLabel":"","text":"Expiry","type":"control_textbox"},{"name":"termsAnd","qid":"58","text":"Terms and Conditions","type":"control_head"},{"name":"ltpgtltspanStylefontweight","qid":"59","text":"PENALTY Notice will be charge $40 per notice  Fuel to be returned at same level provided  Driver is responsible for tolls and traffic finesI have Read and understood and herby accept the terms and conditions  of this agreement","type":"control_text"},null,null,{"name":"submit","qid":"62","text":"Continue","type":"control_button"},null,{"description":"","name":"vehicleRego64","qid":"64","subLabel":"","text":"VEHICLE REGO","type":"control_textbox"},{"description":"","name":"make65","qid":"65","subLabel":"","text":"MAKE","type":"control_textbox"},{"description":"","name":"model66","qid":"66","subLabel":"","text":"MODEL STREET BOB","type":"control_textbox"},{"name":"address604606","qid":"67","text":"Address: 604-606 Parramatta Road Croydon","type":"control_head"},{"description":"","name":"hirer168","qid":"68","subLabel":"","text":"HIRER 1 SIGNATURE","type":"control_signature"},{"description":"","name":"hirer269","qid":"69","subLabel":"","text":"HIRER 2 SIGNATURE","type":"control_signature"},{"description":"","name":"additionaldrivernbspchargesnbsp38day","qid":"70","subLabel":"","text":"Additional Driver Charges @$38\u002Fday","type":"control_textbox"},null,null,{"description":"","name":"hireTime73","qid":"73","subLabel":"","text":"HIRE TIME","type":"control_textbox"}]);}, 20); 
</script>
</head>
<body>
<form class="jotform-form" onsubmit="return typeof testSubmitFunction !== 'undefined' && testSubmitFunction();" action="https://submit.jotform.com/submit/233238940095055" method="post" name="form_233238940095055" id="233238940095055" accept-charset="utf-8" autocomplete="on"><input type="hidden" name="formID" value="233238940095055" /><input type="hidden" id="JWTContainer" value="" /><input type="hidden" id="cardinalOrderNumber" value="" /><input type="hidden" id="jsExecutionTracker" name="jsExecutionTracker" value="build-date-1753766270067" /><input type="hidden" id="submitSource" name="submitSource" value="unknown" /><input type="hidden" id="submitDate" name="submitDate" value="undefined" /><input type="hidden" id="buildDate" name="buildDate" value="1753766270067" /><input type="hidden" name="uploadServerUrl" value="https://upload.jotform.com/upload" /><input type="hidden" name="eventObserver" value="1" />
  <div role="main" class="form-all">
    <ul class="form-section page-section" role="presentation">
      <li id="cid_1" class="form-input-wide" data-type="control_head">
        <div class="form-header-group  header-large">
          <div class="header-text httal htvam">
            <h1 id="header_1" class="form-header" data-component="header">CERTIS RENTALS PTY LTD</h1>
          </div>
        </div>
      </li>
      <li id="cid_2" class="form-input-wide" data-type="control_head">
        <div class="form-header-group  header-small">
          <div class="header-text httal htvam">
            <h3 id="header_2" class="form-header" data-component="header">ABN **************</h3>
          </div>
        </div>
      </li>
      <li id="cid_3" class="form-input-wide" data-type="control_head">
        <div class="form-header-group  header-small">
          <div class="header-text httal htvam">
            <h3 id="header_3" class="form-header" data-component="header">Tel No. 02 7906 842 Address: 604-606 Parramatta Road Croydon</h3>
          </div>
        </div>
      </li>
      <li id="cid_67" class="form-input-wide" data-type="control_head">
        <div class="form-header-group  header-small">
          <div class="header-text httal htvam">
            <h3 id="header_67" class="form-header" data-component="header">Address: 604-606 Parramatta Road Croydon</h3>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_4"><label class="form-label form-label-top form-label-auto" id="label_4" for="input_4" aria-hidden="false"> Rental Contract </label>
        <div id="cid_4" class="form-input-wide" data-layout="half"> <input type="text" id="input_4" name="q4_rentalContract" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_4" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_64"><label class="form-label form-label-top form-label-auto" id="label_64" for="input_64" aria-hidden="false"> VEHICLE REGO </label>
        <div id="cid_64" class="form-input-wide" data-layout="half"> <input type="text" id="input_64" name="q64_vehicleRego64" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:310px" size="310" data-component="textbox" aria-labelledby="label_64" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_65"><label class="form-label form-label-top form-label-auto" id="label_65" for="input_65" aria-hidden="false"> MAKE </label>
        <div id="cid_65" class="form-input-wide" data-layout="half"> <input type="text" id="input_65" name="q65_make65" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:310px" size="310" data-component="textbox" aria-labelledby="label_65" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_66"><label class="form-label form-label-top form-label-auto" id="label_66" for="input_66" aria-hidden="false"> MODEL STREET BOB </label>
        <div id="cid_66" class="form-input-wide" data-layout="half"> <input type="text" id="input_66" name="q66_model66" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:310px" size="310" data-component="textbox" aria-labelledby="label_66" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_datetime" id="id_8"><label class="form-label form-label-top form-label-auto" id="label_8" for="lite_mode_8" aria-hidden="false"> HIRE DATE </label>
        <div id="cid_8" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="month_8" name="q8_hireDate[month]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_8 sublabel_8_month" value="" /><span class="date-separate" aria-hidden="true"> /</span><label class="form-sub-label" for="month_8" id="sublabel_8_month" style="min-height:13px">Month</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="day_8" name="q8_hireDate[day]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_8 sublabel_8_day" value="" /><span class="date-separate" aria-hidden="true"> /</span><label class="form-sub-label" for="day_8" id="sublabel_8_day" style="min-height:13px">Day</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="year_8" name="q8_hireDate[year]" type="tel" size="4" data-maxlength="4" data-age="" maxLength="4" autoComplete="off" aria-labelledby="label_8 sublabel_8_year" value="" /><label class="form-sub-label" for="year_8" id="sublabel_8_year" style="min-height:13px">Year</label></span></div><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate, validateLiteDate]" id="lite_mode_8" type="text" size="12" data-maxlength="12" maxLength="12" data-age="" data-format="mmddyyyy" data-seperator="/" placeholder="MM/DD/YYYY" data-placeholder="MM/DD/YYYY" autoComplete="off" aria-labelledby="label_8 sublabel_8_litemode" value="" /><button type="button" class=" newDefaultTheme-dateIcon focusable icon-liteMode" id="input_8_pick" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v2"></button><label class="form-sub-label" for="lite_mode_8" id="sublabel_8_litemode" style="min-height:13px">Date</label></span>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_73"><label class="form-label form-label-top form-label-auto" id="label_73" for="input_73" aria-hidden="false"> HIRE TIME </label>
        <div id="cid_73" class="form-input-wide" data-layout="half"> <input type="text" id="input_73" name="q73_hireTime73" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:310px" size="310" data-component="textbox" aria-labelledby="label_73" value="" /> </div>
      </li>
      <li id="cid_10" class="form-input-wide" data-type="control_head">
        <div class="form-header-group  header-small">
          <div class="header-text httal htvam">
            <h3 id="header_10" class="form-header" data-component="header">AREA OF USE - Metro Area - Unlimited KMS</h3>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_datetime" id="id_11"><label class="form-label form-label-top form-label-auto" id="label_11" for="lite_mode_11" aria-hidden="false"> Return Date </label>
        <div id="cid_11" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="month_11" name="q11_returnDate[month]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_11 sublabel_11_month" value="" /><span class="date-separate" aria-hidden="true"> /</span><label class="form-sub-label" for="month_11" id="sublabel_11_month" style="min-height:13px">Month</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="day_11" name="q11_returnDate[day]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_11 sublabel_11_day" value="" /><span class="date-separate" aria-hidden="true"> /</span><label class="form-sub-label" for="day_11" id="sublabel_11_day" style="min-height:13px">Day</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="year_11" name="q11_returnDate[year]" type="tel" size="4" data-maxlength="4" data-age="" maxLength="4" autoComplete="off" aria-labelledby="label_11 sublabel_11_year" value="" /><label class="form-sub-label" for="year_11" id="sublabel_11_year" style="min-height:13px">Year</label></span></div><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate, validateLiteDate]" id="lite_mode_11" type="text" size="12" data-maxlength="12" maxLength="12" data-age="" data-format="mmddyyyy" data-seperator="/" placeholder="MM/DD/YYYY" data-placeholder="MM/DD/YYYY" autoComplete="off" aria-labelledby="label_11 sublabel_11_litemode" value="" /><button type="button" class=" newDefaultTheme-dateIcon focusable icon-liteMode" id="input_11_pick" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v2"></button><label class="form-sub-label" for="lite_mode_11" id="sublabel_11_litemode" style="min-height:13px">Date</label></span>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_12"><label class="form-label form-label-top form-label-auto" id="label_12" for="input_12" aria-hidden="false"> Time </label>
        <div id="cid_12" class="form-input-wide" data-layout="half"> <input type="text" id="input_12" name="q12_time" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_12" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_13"><label class="form-label form-label-top form-label-auto" id="label_13" for="input_13" aria-hidden="false"> Hirer 1 Name </label>
        <div id="cid_13" class="form-input-wide" data-layout="half"> <input type="text" id="input_13" name="q13_hirer1" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_13" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_14"><label class="form-label form-label-top form-label-auto" id="label_14" for="input_14" aria-hidden="false"> Fuel Out </label>
        <div id="cid_14" class="form-input-wide" data-layout="half"> <input type="text" id="input_14" name="q14_fuelOut" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_14" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_15"><label class="form-label form-label-top form-label-auto" id="label_15" for="input_15" aria-hidden="false"> Fuel In </label>
        <div id="cid_15" class="form-input-wide" data-layout="half"> <input type="text" id="input_15" name="q15_fuelIn" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_15" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_16"><label class="form-label form-label-top form-label-auto" id="label_16" for="input_16" aria-hidden="false"> Address </label>
        <div id="cid_16" class="form-input-wide" data-layout="half"> <input type="text" id="input_16" name="q16_address" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_16" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_17"><label class="form-label form-label-top form-label-auto" id="label_17" for="input_17" aria-hidden="false"> E 1/4 1/2 3/4 F </label>
        <div id="cid_17" class="form-input-wide" data-layout="half"> <input type="text" id="input_17" name="q17_e14" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_17" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_18"><label class="form-label form-label-top form-label-auto" id="label_18" for="input_18" aria-hidden="false"> E 1/4 1/2 3/4 F </label>
        <div id="cid_18" class="form-input-wide" data-layout="half"> <input type="text" id="input_18" name="q18_e1418" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_18" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_19"><label class="form-label form-label-top form-label-auto" id="label_19" for="input_19" aria-hidden="false"> Suburb </label>
        <div id="cid_19" class="form-input-wide" data-layout="half"> <input type="text" id="input_19" name="q19_suburb" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_19" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_20"><label class="form-label form-label-top form-label-auto" id="label_20" for="input_20" aria-hidden="false"> Charges </label>
        <div id="cid_20" class="form-input-wide" data-layout="half"> <input type="text" id="input_20" name="q20_charges" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_20" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_21"><label class="form-label form-label-top form-label-auto" id="label_21" for="input_21" aria-hidden="false"> State </label>
        <div id="cid_21" class="form-input-wide" data-layout="half"> <input type="text" id="input_21" name="q21_state" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_21" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_22"><label class="form-label form-label-top form-label-auto" id="label_22" for="input_22" aria-hidden="false"> Post Code </label>
        <div id="cid_22" class="form-input-wide" data-layout="half"> <input type="text" id="input_22" name="q22_postCode" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_22" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_23"><label class="form-label form-label-top form-label-auto" id="label_23" for="input_23" aria-hidden="false"> @$ /day </label>
        <div id="cid_23" class="form-input-wide" data-layout="half"> <input type="text" id="input_23" name="q23_day" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_23" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_24"><label class="form-label form-label-top form-label-auto" id="label_24" for="input_24" aria-hidden="false"> @$ /day </label>
        <div id="cid_24" class="form-input-wide" data-layout="half"> <input type="text" id="input_24" name="q24_day24" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_24" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_phone" id="id_25"><label class="form-label form-label-top form-label-auto" id="label_25" for="input_25_full"> Phone </label>
        <div id="cid_25" class="form-input-wide" data-layout="half"> <span class="form-sub-label-container" style="vertical-align:top"><input type="tel" id="input_25_full" name="q25_phone[full]" data-type="mask-number" class="mask-phone-number form-textbox validate[Fill Mask]" data-defaultvalue="" autoComplete="section-input_25 tel-national" style="width:310px" data-masked="true" placeholder="(*************" data-component="phone" aria-labelledby="label_25" value="" /></span> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_26"><label class="form-label form-label-top form-label-auto" id="label_26" for="input_26" aria-hidden="false"> Excess Reduction @$ /day </label>
        <div id="cid_26" class="form-input-wide" data-layout="half"> <input type="text" id="input_26" name="q26_excessReduction" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_26" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_27"><label class="form-label form-label-top form-label-auto" id="label_27" for="input_27" aria-hidden="false"> Excess Reduction @$ /day </label>
        <div id="cid_27" class="form-input-wide" data-layout="half"> <input type="text" id="input_27" name="q27_excessReduction27" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_27" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_28"><label class="form-label form-label-top form-label-auto" id="label_28" for="input_28" aria-hidden="false"> Licence No </label>
        <div id="cid_28" class="form-input-wide" data-layout="half"> <input type="text" id="input_28" name="q28_licenceNo" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_28" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_29"><label class="form-label form-label-top form-label-auto" id="label_29" for="input_29" aria-hidden="false"> State </label>
        <div id="cid_29" class="form-input-wide" data-layout="half"> <input type="text" id="input_29" name="q29_state29" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_29" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_30"><label class="form-label form-label-top form-label-auto" id="label_30" for="input_30" aria-hidden="false"> Fee @$ 6.05/day </label>
        <div id="cid_30" class="form-input-wide" data-layout="half"> <input type="text" id="input_30" name="q30_fee" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_30" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_31"><label class="form-label form-label-top form-label-auto" id="label_31" for="input_31" aria-hidden="false"> DOB </label>
        <div id="cid_31" class="form-input-wide" data-layout="half"> <input type="text" id="input_31" name="q31_dob" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_31" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_datetime" id="id_32"><label class="form-label form-label-top form-label-auto" id="label_32" for="lite_mode_32" aria-hidden="false"> Exp Date </label>
        <div id="cid_32" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="month_32" name="q32_expDate[month]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_32 sublabel_32_month" value="" /><span class="date-separate" aria-hidden="true"> /</span><label class="form-sub-label" for="month_32" id="sublabel_32_month" style="min-height:13px">Month</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="day_32" name="q32_expDate[day]" type="tel" size="2" data-maxlength="2" data-age="" maxLength="2" autoComplete="off" aria-labelledby="label_32 sublabel_32_day" value="" /><span class="date-separate" aria-hidden="true"> /</span><label class="form-sub-label" for="day_32" id="sublabel_32_day" style="min-height:13px">Day</label></span><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate]" id="year_32" name="q32_expDate[year]" type="tel" size="4" data-maxlength="4" data-age="" maxLength="4" autoComplete="off" aria-labelledby="label_32 sublabel_32_year" value="" /><label class="form-sub-label" for="year_32" id="sublabel_32_year" style="min-height:13px">Year</label></span></div><span class="form-sub-label-container" style="vertical-align:top"><input class="form-textbox validate[limitDate, validateLiteDate]" id="lite_mode_32" type="text" size="12" data-maxlength="12" maxLength="12" data-age="" data-format="mmddyyyy" data-seperator="/" placeholder="MM/DD/YYYY" data-placeholder="MM/DD/YYYY" autoComplete="off" aria-labelledby="label_32 sublabel_32_litemode" value="" /><button type="button" class=" newDefaultTheme-dateIcon focusable icon-liteMode" id="input_32_pick" data-component="datetime" aria-hidden="true" data-allow-time="No" data-version="v2"></button><label class="form-sub-label" for="lite_mode_32" id="sublabel_32_litemode" style="min-height:13px">Date</label></span>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_33"><label class="form-label form-label-top form-label-auto" id="label_33" for="input_33" aria-hidden="false"> Delivery/Pick up Fee @$33 </label>
        <div id="cid_33" class="form-input-wide" data-layout="half"> <input type="text" id="input_33" name="q33_deliverypickUp" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_33" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_34"><label class="form-label form-label-top form-label-auto" id="label_34" for="input_34" aria-hidden="false"> Delivery/Pick up Fee @$33 </label>
        <div id="cid_34" class="form-input-wide" data-layout="half"> <input type="text" id="input_34" name="q34_deliverypickUp34" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_34" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_dropdown" id="id_35"><label class="form-label form-label-top form-label-auto" id="label_35" for="input_35" aria-hidden="false"> ADDITIONAL DRIVER: Yes/ No </label>
        <div id="cid_35" class="form-input-wide" data-layout="half"> <select class="form-dropdown" id="input_35" name="q35_additionalDriver" style="width:150px" data-component="dropdown" aria-label="ADDITIONAL DRIVER:     Yes/ No">
            <option value="">Please Select</option>
            <option value="Yes">Yes</option>
            <option value="No">No</option>
            <option value="Select">Select</option>
          </select> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_36"><label class="form-label form-label-top form-label-auto" id="label_36" for="input_36" aria-hidden="false"> ADDITIONAL DRIVER: Yes/ No </label>
        <div id="cid_36" class="form-input-wide" data-layout="half"> <input type="text" id="input_36" name="q36_additionalDriver36" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_36" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_37"><label class="form-label form-label-top form-label-auto" id="label_37" for="input_37" aria-hidden="false"> Helmet </label>
        <div id="cid_37" class="form-input-wide" data-layout="half"> <input type="text" id="input_37" name="q37_helmet" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_37" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_38"><label class="form-label form-label-top form-label-auto" id="label_38" for="input_38" aria-hidden="false"> /day </label>
        <div id="cid_38" class="form-input-wide" data-layout="half"> <input type="text" id="input_38" name="q38_day38" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_38" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_39"><label class="form-label form-label-top form-label-auto" id="label_39" for="input_39" aria-hidden="false"> Riding Apparel </label>
        <div id="cid_39" class="form-input-wide" data-layout="half"> <input type="text" id="input_39" name="q39_ridingApparel" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_39" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_40"><label class="form-label form-label-top form-label-auto" id="label_40" for="input_40" aria-hidden="false"> Additional Driver Name </label>
        <div id="cid_40" class="form-input-wide" data-layout="half"> <input type="text" id="input_40" name="q40_additionalDriver40" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_40" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_41"><label class="form-label form-label-top form-label-auto" id="label_41" for="input_41" aria-hidden="false"> Admin Fee @ 4.8 </label>
        <div id="cid_41" class="form-input-wide" data-layout="half"> <input type="text" id="input_41" name="q41_adminFee" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_41" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_divider" id="id_42">
        <div id="cid_42" class="form-input-wide" data-layout="full">
          <div class="divider" data-component="divider" style="border-bottom-width:1px;border-bottom-style:solid;border-color:#e6e6e6;height:1px;margin-left:0px;margin-right:0px;margin-top:5px;margin-bottom:5px"></div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_43"><label class="form-label form-label-top form-label-auto" id="label_43" for="input_43" aria-hidden="false"> Admin Fee @ 4.8 </label>
        <div id="cid_43" class="form-input-wide" data-layout="half"> <input type="text" id="input_43" name="q43_adminFee43" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_43" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_divider" id="id_44">
        <div id="cid_44" class="form-input-wide" data-layout="full">
          <div class="divider" data-component="divider" style="border-bottom-width:1px;border-bottom-style:solid;border-color:#e6e6e6;height:1px;margin-left:0px;margin-right:0px;margin-top:5px;margin-bottom:5px"></div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_45"><label class="form-label form-label-top form-label-auto" id="label_45" for="input_45" aria-hidden="false"> Admin Fee @ 4.8 </label>
        <div id="cid_45" class="form-input-wide" data-layout="half"> <input type="text" id="input_45" name="q45_adminFee45" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_45" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_divider" id="id_46">
        <div id="cid_46" class="form-input-wide" data-layout="full">
          <div class="divider" data-component="divider" style="border-bottom-width:1px;border-bottom-style:solid;border-color:#e6e6e6;height:1px;margin-left:0px;margin-right:0px;margin-top:5px;margin-bottom:5px"></div>
        </div>
      </li>
      <li class="form-line" data-type="control_address" id="id_47" data-compound-hint=",,,,Please Select,,Please Select,"><label class="form-label form-label-top form-label-auto" id="label_47" for="input_47_addr_line1" aria-hidden="false"> Address </label>
        <div id="cid_47" class="form-input-wide" data-layout="full">
          <div summary="" class="form-address-table jsTest-addressField">
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField"><span class="form-address-line form-address-street-line jsTest-address-lineField"><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_47_addr_line1" name="q47_address47[addr_line1]" class="form-textbox form-address-line" data-defaultvalue="" autoComplete="section-input_47 address-line1" data-component="address_line_1" aria-labelledby="label_47 sublabel_47_addr_line1" value="" /><label class="form-sub-label" for="input_47_addr_line1" id="sublabel_47_addr_line1" style="min-height:13px">Address</label></span></span></div>
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField" style="display:none"><span class="form-address-line form-address-street-line jsTest-address-lineField"><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_47_addr_line2" name="q47_address47[addr_line2]" class="form-textbox form-address-line" data-defaultvalue="" autoComplete="section-input_47 off" data-component="address_line_2" aria-labelledby="label_47 sublabel_47_addr_line2" value="" /><label class="form-sub-label" for="input_47_addr_line2" id="sublabel_47_addr_line2" style="min-height:13px">Street Address Line 2</label></span></span></div>
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField"><span class="form-address-line form-address-city-line jsTest-address-lineField "><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_47_city" name="q47_address47[city]" class="form-textbox form-address-city" data-defaultvalue="" autoComplete="section-input_47 address-level2" data-component="city" aria-labelledby="label_47 sublabel_47_city" value="" /><label class="form-sub-label" for="input_47_city" id="sublabel_47_city" style="min-height:13px">Suburb</label></span></span><span class="form-address-line form-address-state-line jsTest-address-lineField form-address-hiddenLine" style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_47_state" name="q47_address47[state]" class="form-textbox form-address-state" data-defaultvalue="" autoComplete="section-input_47 off" data-component="state" aria-labelledby="label_47 sublabel_47_state" value="" /><label class="form-sub-label" for="input_47_state" id="sublabel_47_state" style="min-height:13px">State / Province</label></span></span></div>
            <div class="form-address-line-wrapper jsTest-address-line-wrapperField" style="display:none"><span class="form-address-line form-address-zip-line jsTest-address-lineField form-address-hiddenLine" style="display:none"><span class="form-sub-label-container" style="vertical-align:top"><input type="text" id="input_47_postal" name="q47_address47[postal]" class="form-textbox form-address-postal" data-defaultvalue="" autoComplete="section-input_47 off" data-component="zip" aria-labelledby="label_47 sublabel_47_postal" value="" /><label class="form-sub-label" for="input_47_postal" id="sublabel_47_postal" style="min-height:13px">Postal / Zip Code</label></span></span></div>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_70"><label class="form-label form-label-top form-label-auto" id="label_70" for="input_70" aria-hidden="false"> Additional Driver Charges @$38/day </label>
        <div id="cid_70" class="form-input-wide" data-layout="half"> <input type="text" id="input_70" name="q70_additionaldrivernbspchargesnbsp38day" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:310px" size="310" data-component="textbox" aria-labelledby="label_70" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_49"><label class="form-label form-label-top form-label-auto" id="label_49" for="input_49" aria-hidden="false"> Suburb </label>
        <div id="cid_49" class="form-input-wide" data-layout="half"> <input type="text" id="input_49" name="q49_suburb49" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_49" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_50"><label class="form-label form-label-top form-label-auto" id="label_50" for="input_50" aria-hidden="false"> TOTAL inc GST </label>
        <div id="cid_50" class="form-input-wide" data-layout="half"> <input type="text" id="input_50" name="q50_totalInc" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_50" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_51"><label class="form-label form-label-top form-label-auto" id="label_51" for="input_51" aria-hidden="false"> GST Amount </label>
        <div id="cid_51" class="form-input-wide" data-layout="half"> <input type="text" id="input_51" name="q51_gstAmount" data-type="input-textbox" class="form-textbox" data-defaultvalue="$" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_51" value="$" /> </div>
      </li>
      <li class="form-line" data-type="control_phone" id="id_53"><label class="form-label form-label-top form-label-auto" id="label_53" for="input_53_full"> Phone </label>
        <div id="cid_53" class="form-input-wide" data-layout="half"> <span class="form-sub-label-container" style="vertical-align:top"><input type="tel" id="input_53_full" name="q53_phone53[full]" data-type="mask-number" class="mask-phone-number form-textbox validate[Fill Mask]" data-defaultvalue="" autoComplete="section-input_53 tel-national" style="width:310px" data-masked="true" placeholder="(*************" data-component="phone" aria-labelledby="label_53" value="" /></span> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_54"><label class="form-label form-label-top form-label-auto" id="label_54" for="input_54" aria-hidden="false"> Licence No. </label>
        <div id="cid_54" class="form-input-wide" data-layout="half"> <input type="text" id="input_54" name="q54_licenceNo54" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_54" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_55"><label class="form-label form-label-top form-label-auto" id="label_55" for="input_55" aria-hidden="false"> State </label>
        <div id="cid_55" class="form-input-wide" data-layout="half"> <input type="text" id="input_55" name="q55_state55" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_55" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_56"><label class="form-label form-label-top form-label-auto" id="label_56" for="input_56" aria-hidden="false"> DOB </label>
        <div id="cid_56" class="form-input-wide" data-layout="half"> <input type="text" id="input_56" name="q56_dob56" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_56" value="" /> </div>
      </li>
      <li class="form-line" data-type="control_textbox" id="id_57"><label class="form-label form-label-top form-label-auto" id="label_57" for="input_57" aria-hidden="false"> Expiry </label>
        <div id="cid_57" class="form-input-wide" data-layout="half"> <input type="text" id="input_57" name="q57_expiry" data-type="input-textbox" class="form-textbox" data-defaultvalue="" style="width:20px" size="20" data-component="textbox" aria-labelledby="label_57" value="" /> </div>
      </li>
      <li id="cid_58" class="form-input-wide" data-type="control_head">
        <div class="form-header-group  header-small">
          <div class="header-text httal htvam">
            <h3 id="header_58" class="form-header" data-component="header">Terms and Conditions</h3>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_text" id="id_59">
        <div id="cid_59" class="form-input-wide" data-layout="full">
          <div id="text_59" class="form-html" data-component="text" tabindex="-1">
            <p><span style="font-weight: bold;color: #2c2e35;">PENALTY Notice</span><span style="color: #2c2e35;"> will be charge $40 per notice </span> <span style="color: #2c2e35;">Fuel to be returned at same level provided </span> <span style="color: #2c2e35;">Driver is responsible for tolls and traffic fines</span></p>
            <p><span style="color: #2c2e35;">I have Read and understood and herby accept the terms and</span> <span style="color: #2c2e35;">conditions of this agreement</span></p>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_signature" id="id_68"><label class="form-label form-label-top form-label-auto" id="label_68" for="input_68" aria-hidden="false"> HIRER 1 SIGNATURE </label>
        <div id="cid_68" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div id="signature_pad_68" class="signature-pad-wrapper">
              <div data-wrapper-react="true">
                <!--[if IE 7]><script type="text/javascript" src="/js/vendor/json2.js"></script><![endif]-->
              </div>
              <div class="signature-line signature-wrapper signature-placeholder" data-component="signature">
                <div id="sig_pad_68" data-width="310" data-height="114" data-id="68" data-required="false" class="pad " aria-description="Use your pointer or touch input to draw your signature." aria-labelledby="label_68" tabindex="0"></div><input type="hidden" name="q68_hirer168" class="output4" id="input_68" />
              </div>
              <aside class="signature-pad-aside"><a style="margin-top:2px;font-size:10px;color:inherit;text-decoration:none" href="https://www.jotform.com/products/sign?utm_source=sign_cardform&amp;utm_content=form&amp;utm_medium=button&amp;utm_campaign=sign_form_integration" target="_blank">Powered by <span style="color:#57810b;font-weight:700">Jotform Sign</span></a><span class="clear-pad-btn clear-pad" role="button" tabindex="0">Clear</span></aside>
            </div>
            <div data-wrapper-react="true">
              <script type="text/javascript">
                window.signatureForm = true
              </script>
            </div>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_signature" id="id_69"><label class="form-label form-label-top form-label-auto" id="label_69" for="input_69" aria-hidden="false"> HIRER 2 SIGNATURE </label>
        <div id="cid_69" class="form-input-wide" data-layout="half">
          <div data-wrapper-react="true">
            <div id="signature_pad_69" class="signature-pad-wrapper">
              <div data-wrapper-react="true">
                <!--[if IE 7]><script type="text/javascript" src="/js/vendor/json2.js"></script><![endif]-->
              </div>
              <div class="signature-line signature-wrapper signature-placeholder" data-component="signature">
                <div id="sig_pad_69" data-width="310" data-height="114" data-id="69" data-required="false" class="pad " aria-description="Use your pointer or touch input to draw your signature." aria-labelledby="label_69" tabindex="0"></div><input type="hidden" name="q69_hirer269" class="output4" id="input_69" />
              </div>
              <aside class="signature-pad-aside"><a style="margin-top:2px;font-size:10px;color:inherit;text-decoration:none" href="https://www.jotform.com/products/sign?utm_source=sign_cardform&amp;utm_content=form&amp;utm_medium=button&amp;utm_campaign=sign_form_integration" target="_blank">Powered by <span style="color:#57810b;font-weight:700">Jotform Sign</span></a><span class="clear-pad-btn clear-pad" role="button" tabindex="0">Clear</span></aside>
            </div>
            <div data-wrapper-react="true">
              <script type="text/javascript">
                window.signatureForm = true
              </script>
            </div>
          </div>
        </div>
      </li>
      <li class="form-line" data-type="control_button" id="id_62">
        <div id="cid_62" class="form-input-wide" data-layout="full">
          <div data-align="auto" class="form-buttons-wrapper form-buttons-auto   jsTest-button-wrapperField"><button id="input_62" style="display:none !important" type="button" class="form-submit-button submit-button jf-form-buttons jsTest-submitField " data-component="button" data-content="">Continue</button><button type="button" class="form-submit-button submit-button jf-form-buttons jsTest-submitField useJotformSign-button useJotformSign " data-component="button" data-content="">Continue</button></div>
        </div>
      </li>
      <li style="display:none">Should be Empty: <input type="text" name="website" value="" type="hidden" /></li>
    </ul>
  </div>
  <script>
    JotForm.showJotFormPowered = "new_footer";
  </script>
  <script>
    JotForm.poweredByText = "Powered by Jotform";
  </script><input type="hidden" class="simple_spc" id="simple_spc" name="simple_spc" value="233238940095055" />
  <script type="text/javascript">
    var all_spc = document.querySelectorAll("form[id='233238940095055'] .si" + "mple" + "_spc");
    for (var i = 0; i < all_spc.length; i++)
    {
      all_spc[i].value = "233238940095055-233238940095055";
    }
  </script>
</form></body>
</html><script type="text/javascript">JotForm.isNewSACL=true;</script>