const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Port your Next.js app is running on
const PORT = 9003;
const ENV_FILE_PATH = path.join(__dirname, '.env.local');

// Function to update .env.local with the ngrok URL
function updateEnvFile(url) {
  let envContent = '';
  
  try {
    if (fs.existsSync(ENV_FILE_PATH)) {
      envContent = fs.readFileSync(ENV_FILE_PATH, 'utf8');
    }
  } catch (err) {
    console.log('Creating new .env.local file...');
  }

  // Replace or add NEXT_PUBLIC_BASE_URL
  if (envContent.includes('NEXT_PUBLIC_BASE_URL=')) {
    envContent = envContent.replace(/NEXT_PUBLIC_BASE_URL=.*/, `NEXT_PUBLIC_BASE_URL=${url}`);
  } else {
    envContent += `\nNEXT_PUBLIC_BASE_URL=${url}\n`;
  }

  // Write to .env.local
  fs.writeFileSync(ENV_FILE_PATH, envContent);
  console.log(`✅ Updated .env.local with NEXT_PUBLIC_BASE_URL=${url}`);
}

// Start ngrok as a child process
console.log(`🚀 Starting ngrok on port ${PORT}...`);
const ngrokProcess = spawn('ngrok', ['http', PORT.toString()], { 
  stdio: ['ignore', 'pipe', 'pipe'],
  shell: true 
});

let urlFound = false;

ngrokProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log(output);

  // Extract the URL from ngrok output
  if (!urlFound) {
    const matches = output.match(/(https:\/\/[a-z0-9]+\.ngrok\.io)/i);
    if (matches && matches[1]) {
      const url = matches[1];
      urlFound = true;
      
      // Update .env.local with the ngrok URL
      updateEnvFile(url);
      
      console.log('\n📋 NEXT STEPS:');
      console.log('1. Restart your Next.js server to apply the new environment variable');
      console.log('2. Your form links in emails will now use this public URL');
      console.log(`3. To test, try sending yourself a form email`);
      console.log('\n⚠️ This tunnel will close when you press Ctrl+C');
      console.log('Keep this window open while testing your form links!\n');
    }
  }
});

ngrokProcess.stderr.on('data', (data) => {
  console.error(`⚠️ Error: ${data.toString()}`);
});

ngrokProcess.on('close', (code) => {
  console.log(`ngrok process exited with code ${code}`);
});

// Handle Ctrl+C to clean up
process.on('SIGINT', () => {
  console.log('\nShutting down ngrok...');
  ngrokProcess.kill();
  process.exit();
});
