const ngrok = require('ngrok');
const fs = require('fs');
const path = require('path');

// Port your Next.js app is running on
const PORT = 9003;

async function setupNgrok() {
  try {
    console.clear();
    console.log('🔌 Setting up ngrok tunnel...');
    console.log(`📡 Connecting to your local Next.js server on port ${PORT}...`);
    
    // Start ngrok tunnel - using authtoken free tier
    const url = await ngrok.connect({
      addr: PORT,
      onStatusChange: status => {
        console.log(`Ngrok status: ${status}`);
      },
    });

    console.log('\n✅ SUCCESS! Ngrok tunnel established!');
    console.log(`🌎 Public URL: ${url}`);
    console.log('\n👉 Your forms will now be accessible through emails using this public URL');

    // Create or update .env.local file with the ngrok URL
    const envFilePath = path.join(__dirname, '.env.local');
    let envContent = '';
    
    // Try to read existing .env.local file
    try {
      envContent = fs.readFileSync(envFilePath, 'utf8');
    } catch (err) {
      // File doesn't exist, create a new one
      console.log('Creating new .env.local file...');
    }

    // Replace or add NEXT_PUBLIC_BASE_URL
    if (envContent.includes('NEXT_PUBLIC_BASE_URL=')) {
      envContent = envContent.replace(
        /NEXT_PUBLIC_BASE_URL=.*/,
        `NEXT_PUBLIC_BASE_URL=${url}`
      );
    } else {
      envContent += `\nNEXT_PUBLIC_BASE_URL=${url}\n`;
    }

    // Write updated content to .env.local
    fs.writeFileSync(envFilePath, envContent);
    console.log(`✅ Updated .env.local with NEXT_PUBLIC_BASE_URL=${url}`);
    
    // Instructions for the user
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Restart your Next.js server to apply the new environment variable');
    console.log('2. Your form links in emails will now use this public URL');
    console.log('3. The ngrok URL will change each time you run this script (free tier limitation)');
    console.log('\n⚠️ This tunnel will close when you press Ctrl+C');
    console.log('Keep this window open while testing your form links!\n');

    // Keep the process running until Ctrl+C
    process.on('SIGINT', async () => {
      console.log('\nShutting down ngrok tunnel...');
      await ngrok.kill();
      console.log('Tunnel closed. Goodbye!');
      process.exit();
    });
    
  } catch (error) {
    console.error('Error setting up ngrok tunnel:', error);
    
    if (error.message && error.message.includes('EPERM')) {
      console.log('\n⚠️ PERMISSION ERROR: Unable to start ngrok.');
      console.log('This may be due to Windows security restrictions.');
      console.log('\nPlease try one of these solutions:');
      console.log('1. Run your terminal as Administrator');
      console.log('2. Install ngrok globally: npm install -g ngrok');
      console.log('3. Or use the ngrok desktop application from https://ngrok.com/download');
    }
    
    process.exit(1);
  }
}

setupNgrok();
