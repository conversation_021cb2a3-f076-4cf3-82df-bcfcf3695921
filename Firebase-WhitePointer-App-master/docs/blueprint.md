# **App Name**: PBikeRescue Rails

## Core Features:

- Case Management: Manage motorbike rental cases.
- Fleet Tracking: Track motorbike inventory and assignments.
- Financial Records: Handle financial records and transactions.
- AI Email Generation: Generate AI-assisted email templates for collections with varying tones and purposes using a LLM as a tool.
- Insurance Management: Store and manage insurance provider details.
- Document Storage: Store documents associated with the motorbike rental cases.
- Subscription Management: Manage user subscriptions to PBikeRescue

## Style Guidelines:

- Primary color: #3B82F6 (HSL: 212, 89%, 59%), a vibrant blue that suggests trust and reliability, aligning with the professional nature of rental management. This color works well within a light color scheme, and provides sufficient contrast when used with white text.
- Background color: #E5E7EB (HSL: 0, 17%, 84%), a very light grey that ensures a clean, modern backdrop, making content easy to read and the primary color pop.
- Accent color: #6366F1 (HSL: 244, 76%, 64%), an analogous, slightly more saturated and brighter violet, is used to highlight calls to action and interactive elements, drawing the user's eye without disrupting the overall harmony.
- Body and headline font: 'Inter', a grotesque-style sans-serif known for its clean and modern appearance, ideal for readability in both headlines and body text. Note: currently only Google Fonts are supported.
- Code font: 'Source Code Pro' for displaying code snippets. Note: currently only Google Fonts are supported.
- Use consistent, modern icons from a library like FontAwesome or Remix Icon for intuitive navigation and visual appeal.
- Employ a clean, grid-based layout with ample white space to ensure information is easily digestible and the user interface feels open and uncluttered.