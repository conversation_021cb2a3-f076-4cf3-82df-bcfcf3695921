#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Script to test email signature workflow with ngrok
console.log('🧪 Email Signature Testing Setup with Ngrok');
console.log('==========================================');

// Check if ngrok is available
const ngrokPath = path.join(__dirname, 'ngrok-bin', 'ngrok.exe');
if (!fs.existsSync(ngrokPath)) {
  console.error('❌ Ngrok not found in ngrok-bin directory');
  console.log('Please ensure ngrok is installed in the ngrok-bin directory');
  process.exit(1);
}

console.log('✅ Ngrok found');

// Check if development server is running
console.log('\n📋 Pre-flight checklist:');
console.log('1. Make sure your development server is running: npm run dev');
console.log('2. This script will start ngrok tunnel on port 9003');
console.log('3. Copy the ngrok URL and update your environment');
console.log('4. Test email signature forms from external devices');

console.log('\n🚀 Starting ngrok tunnel for email signature testing...');

// Start ngrok
const ngrokProcess = spawn(ngrokPath, ['http', '9003'], {
  stdio: 'inherit',
  cwd: __dirname
});

ngrokProcess.on('close', (code) => {
  console.log(`\n🔴 Ngrok tunnel closed with code ${code}`);
});

// Handle cleanup
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down ngrok tunnel...');
  ngrokProcess.kill();
  process.exit();
});

console.log('\n📧 Email Signature Testing Instructions:');
console.log('1. Copy the HTTPS ngrok URL from above');
console.log('2. Run: node setup-ngrok-env.js https://your-ngrok-url.ngrok.io');
console.log('3. Restart your development server to pick up new URL');
console.log('4. Send test emails with signature forms');
console.log('5. Access forms from mobile devices using the ngrok URL');
console.log('6. Test PDF generation and signature capture');
console.log('\n⚠️  Keep this window open during testing!');