#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// <PERSON>ript to automatically update .env.local with ngrok URL
function updateEnvWithNgrokUrl(ngrokUrl) {
  const envPath = path.join(__dirname, '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.error('.env.local file not found!');
    process.exit(1);
  }
  
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  // Replace the NEXT_PUBLIC_BASE_URL line
  const urlRegex = /NEXT_PUBLIC_BASE_URL=.*/;
  const newUrl = `NEXT_PUBLIC_BASE_URL=${ngrokUrl}`;
  
  if (urlRegex.test(envContent)) {
    envContent = envContent.replace(urlRegex, newUrl);
  } else {
    envContent += `\n${newUrl}\n`;
  }
  
  fs.writeFileSync(envPath, envContent);
  console.log(`✅ Updated .env.local with ngrok URL: ${ngrokUrl}`);
}

// Get ngrok URL from command line argument
const ngrokUrl = process.argv[2];

if (!ngrokUrl) {
  console.error('Please provide the ngrok URL as an argument');
  console.log('Usage: node setup-ngrok-env.js https://abc123.ngrok.io');
  process.exit(1);
}

if (!ngrokUrl.startsWith('https://') || !ngrokUrl.includes('.ngrok.io')) {
  console.error('Invalid ngrok URL format. Expected format: https://abc123.ngrok.io');
  process.exit(1);
}

updateEnvWithNgrokUrl(ngrokUrl);