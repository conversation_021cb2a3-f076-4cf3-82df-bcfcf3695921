import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import db from '../../../lib/db';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const form = formidable({
      uploadDir: path.join(process.cwd(), 'public/uploads/bikes'),
      keepExtensions: true,
      maxFileSize: 5 * 1024 * 1024, // 5MB limit
      filter: ({ mimetype }) => mimetype && mimetype.includes('image'),
    });

    // Ensure upload directory exists
    const uploadDir = path.join(process.cwd(), 'public/uploads/bikes');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const [fields, files] = await form.parse(req);
    const bikeId = fields.bikeId[0];
    const imageFile = files.image[0];

    if (!imageFile) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const extension = path.extname(imageFile.originalFilename || '.jpg');
    const filename = `bike-${bikeId}-${timestamp}${extension}`;
    const newPath = path.join(uploadDir, filename);

    // Move file to permanent location
    fs.renameSync(imageFile.filepath, newPath);

    // Update database with image URL
    const imageUrl = `/uploads/bikes/${filename}`;

    const updateStmt = db.prepare(`
      UPDATE vehicles 
      SET image_url = ?, updated = strftime('%Y-%m-%dT%H:%M:%fZ')
      WHERE id = ?
    `);

    updateStmt.run(imageUrl, bikeId);

    // Log the upload
    console.log(`Image uploaded for bike ${bikeId}: ${imageUrl}`);

    res.status(200).json({
      success: true,
      imageUrl,
      message: 'Image uploaded successfully'
    });

  } catch (error) {
    console.error('Image upload error:', error);
    res.status(500).json({
      error: 'Failed to upload image',
      details: error.message
    });
  }
}
